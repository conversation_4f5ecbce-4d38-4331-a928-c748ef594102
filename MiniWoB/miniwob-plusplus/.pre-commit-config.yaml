---
repos:
  - repo: https://github.com/python/black
    rev: 23.3.0
    hooks:
      - id: black
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.4
    hooks:
      - id: codespell
        exclude: ^(miniwob/html/)|(viewer/)|(.*\.svg$)
        args:
          - --ignore-words-list=sur,vally,nam
  - repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args:
          - '--per-file-ignores=*/__init__.py:F401'
          - --max-complexity=30
          - --max-line-length=456
          - --show-source
          - --statistics
  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0  # pick a git hash / tag to point to
    hooks:
      - id: pydocstyle
        exclude: ^(tests/)|(miniwob/envs/)
        args:
          - --source
          - --explain
          - --convention=google
        additional_dependencies: ["toml"]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.4.0
    hooks:
      - id: pyupgrade
        args: ["--py37-plus"]
  - repo: local
    hooks:
      - id: pyright
        name: pyright
        entry: pyright
        language: node
        pass_filenames: false
        types: [python]
        additional_dependencies: ["pyright@1.1.308"]
        args:
          - --project=pyproject.toml
