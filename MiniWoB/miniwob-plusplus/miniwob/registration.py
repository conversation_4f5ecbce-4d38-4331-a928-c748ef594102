"""Gymnasium environment registration."""
from gymnasium.envs.registration import register


def register_miniwob_envs():
    """Register all MiniWoB environments."""
    register(
        id="miniwob/bisect-angle-v1",
        entry_point="miniwob.envs.miniwob_envs:BisectAngleEnv",
    )
    register(
        id="miniwob/book-flight-v1",
        entry_point="miniwob.envs.miniwob_envs:BookFlightEnv",
    )
    register(
        id="miniwob/book-flight-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:BookFlightNodelayEnv",
    )
    register(
        id="miniwob/choose-date-v1",
        entry_point="miniwob.envs.miniwob_envs:ChooseDateEnv",
    )
    register(
        id="miniwob/choose-date-easy-v1",
        entry_point="miniwob.envs.miniwob_envs:ChooseDateEasyEnv",
    )
    register(
        id="miniwob/choose-date-medium-v1",
        entry_point="miniwob.envs.miniwob_envs:ChooseDateMediumEnv",
    )
    register(
        id="miniwob/choose-date-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:ChooseDateNodelayEnv",
    )
    register(
        id="miniwob/choose-list-v1",
        entry_point="miniwob.envs.miniwob_envs:ChooseListEnv",
    )
    register(
        id="miniwob/circle-center-v1",
        entry_point="miniwob.envs.miniwob_envs:CircleCenterEnv",
    )
    register(
        id="miniwob/click-button-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickButtonEnv",
    )
    register(
        id="miniwob/click-button-sequence-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickButtonSequenceEnv",
    )
    register(
        id="miniwob/click-checkboxes-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCheckboxesEnv",
    )
    register(
        id="miniwob/click-checkboxes-large-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCheckboxesLargeEnv",
    )
    register(
        id="miniwob/click-checkboxes-soft-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCheckboxesSoftEnv",
    )
    register(
        id="miniwob/click-checkboxes-transfer-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCheckboxesTransferEnv",
    )
    register(
        id="miniwob/click-collapsible-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCollapsibleEnv",
    )
    register(
        id="miniwob/click-collapsible-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCollapsible2Env",
    )
    register(
        id="miniwob/click-collapsible-2-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCollapsible2NodelayEnv",
    )
    register(
        id="miniwob/click-collapsible-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickCollapsibleNodelayEnv",
    )
    register(
        id="miniwob/click-color-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickColorEnv",
    )
    register(
        id="miniwob/click-dialog-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickDialogEnv",
    )
    register(
        id="miniwob/click-dialog-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickDialog2Env",
    )
    register(
        id="miniwob/click-link-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickLinkEnv",
    )
    register(
        id="miniwob/click-menu-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickMenuEnv",
    )
    register(
        id="miniwob/click-menu-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickMenu2Env",
    )
    register(
        id="miniwob/click-option-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickOptionEnv",
    )
    register(
        id="miniwob/click-pie-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickPieEnv",
        nondeterministic=True,
    )
    register(
        id="miniwob/click-pie-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickPieNodelayEnv",
        nondeterministic=True,
    )
    register(
        id="miniwob/click-scroll-list-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickScrollListEnv",
    )
    register(
        id="miniwob/click-shades-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickShadesEnv",
    )
    register(
        id="miniwob/click-shape-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickShapeEnv",
    )
    register(
        id="miniwob/click-tab-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTabEnv",
    )
    register(
        id="miniwob/click-tab-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTab2Env",
    )
    register(
        id="miniwob/click-tab-2-easy-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTab2EasyEnv",
    )
    register(
        id="miniwob/click-tab-2-hard-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTab2HardEnv",
    )
    register(
        id="miniwob/click-tab-2-medium-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTab2MediumEnv",
    )
    register(
        id="miniwob/click-test-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTestEnv",
    )
    register(
        id="miniwob/click-test-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTest2Env",
    )
    register(
        id="miniwob/click-test-transfer-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickTestTransferEnv",
    )
    register(
        id="miniwob/click-widget-v1",
        entry_point="miniwob.envs.miniwob_envs:ClickWidgetEnv",
    )
    register(
        id="miniwob/copy-paste-v1",
        entry_point="miniwob.envs.miniwob_envs:CopyPasteEnv",
    )
    register(
        id="miniwob/copy-paste-2-v1",
        entry_point="miniwob.envs.miniwob_envs:CopyPaste2Env",
    )
    register(
        id="miniwob/count-shape-v1",
        entry_point="miniwob.envs.miniwob_envs:CountShapeEnv",
    )
    register(
        id="miniwob/count-sides-v1",
        entry_point="miniwob.envs.miniwob_envs:CountSidesEnv",
    )
    register(
        id="miniwob/drag-box-v1",
        entry_point="miniwob.envs.miniwob_envs:DragBoxEnv",
    )
    register(
        id="miniwob/drag-circle-v1",
        entry_point="miniwob.envs.miniwob_envs:DragCircleEnv",
    )
    register(
        id="miniwob/drag-cube-v1",
        entry_point="miniwob.envs.miniwob_envs:DragCubeEnv",
    )
    register(
        id="miniwob/drag-items-v1",
        entry_point="miniwob.envs.miniwob_envs:DragItemsEnv",
    )
    register(
        id="miniwob/drag-items-grid-v1",
        entry_point="miniwob.envs.miniwob_envs:DragItemsGridEnv",
    )
    register(
        id="miniwob/drag-shapes-v1",
        entry_point="miniwob.envs.miniwob_envs:DragShapesEnv",
    )
    register(
        id="miniwob/drag-sort-numbers-v1",
        entry_point="miniwob.envs.miniwob_envs:DragSortNumbersEnv",
    )
    register(
        id="miniwob/email-inbox-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxEnv",
    )
    register(
        id="miniwob/email-inbox-delete-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxDeleteEnv",
    )
    register(
        id="miniwob/email-inbox-forward-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxForwardEnv",
    )
    register(
        id="miniwob/email-inbox-forward-nl-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxForwardNlEnv",
    )
    register(
        id="miniwob/email-inbox-forward-nl-turk-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxForwardNlTurkEnv",
    )
    register(
        id="miniwob/email-inbox-important-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxImportantEnv",
    )
    register(
        id="miniwob/email-inbox-nl-turk-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxNlTurkEnv",
    )
    register(
        id="miniwob/email-inbox-noscroll-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxNoscrollEnv",
    )
    register(
        id="miniwob/email-inbox-reply-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxReplyEnv",
    )
    register(
        id="miniwob/email-inbox-star-reply-v1",
        entry_point="miniwob.envs.miniwob_envs:EmailInboxStarReplyEnv",
    )
    register(
        id="miniwob/enter-date-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterDateEnv",
    )
    register(
        id="miniwob/enter-password-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterPasswordEnv",
    )
    register(
        id="miniwob/enter-text-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterTextEnv",
    )
    register(
        id="miniwob/enter-text-2-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterText2Env",
    )
    register(
        id="miniwob/enter-text-dynamic-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterTextDynamicEnv",
    )
    register(
        id="miniwob/enter-time-v1",
        entry_point="miniwob.envs.miniwob_envs:EnterTimeEnv",
    )
    register(
        id="miniwob/find-midpoint-v1",
        entry_point="miniwob.envs.miniwob_envs:FindMidpointEnv",
    )
    register(
        id="miniwob/find-word-v1",
        entry_point="miniwob.envs.miniwob_envs:FindWordEnv",
    )
    register(
        id="miniwob/focus-text-v1",
        entry_point="miniwob.envs.miniwob_envs:FocusTextEnv",
    )
    register(
        id="miniwob/focus-text-2-v1",
        entry_point="miniwob.envs.miniwob_envs:FocusText2Env",
    )
    register(
        id="miniwob/grid-coordinate-v1",
        entry_point="miniwob.envs.miniwob_envs:GridCoordinateEnv",
    )
    register(
        id="miniwob/guess-number-v1",
        entry_point="miniwob.envs.miniwob_envs:GuessNumberEnv",
    )
    register(
        id="miniwob/highlight-text-v1",
        entry_point="miniwob.envs.miniwob_envs:HighlightTextEnv",
    )
    register(
        id="miniwob/highlight-text-2-v1",
        entry_point="miniwob.envs.miniwob_envs:HighlightText2Env",
    )
    register(
        id="miniwob/identify-shape-v1",
        entry_point="miniwob.envs.miniwob_envs:IdentifyShapeEnv",
    )
    register(
        id="miniwob/login-user-v1",
        entry_point="miniwob.envs.miniwob_envs:LoginUserEnv",
    )
    register(
        id="miniwob/login-user-popup-v1",
        entry_point="miniwob.envs.miniwob_envs:LoginUserPopupEnv",
    )
    register(
        id="miniwob/multi-layouts-v1",
        entry_point="miniwob.envs.miniwob_envs:MultiLayoutsEnv",
    )
    register(
        id="miniwob/multi-orderings-v1",
        entry_point="miniwob.envs.miniwob_envs:MultiOrderingsEnv",
    )
    register(
        id="miniwob/navigate-tree-v1",
        entry_point="miniwob.envs.miniwob_envs:NavigateTreeEnv",
    )
    register(
        id="miniwob/number-checkboxes-v1",
        entry_point="miniwob.envs.miniwob_envs:NumberCheckboxesEnv",
    )
    register(
        id="miniwob/read-table-v1",
        entry_point="miniwob.envs.miniwob_envs:ReadTableEnv",
    )
    register(
        id="miniwob/read-table-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ReadTable2Env",
    )
    register(
        id="miniwob/resize-textarea-v1",
        entry_point="miniwob.envs.miniwob_envs:ResizeTextareaEnv",
    )
    register(
        id="miniwob/right-angle-v1",
        entry_point="miniwob.envs.miniwob_envs:RightAngleEnv",
    )
    register(
        id="miniwob/scroll-text-v1",
        entry_point="miniwob.envs.miniwob_envs:ScrollTextEnv",
    )
    register(
        id="miniwob/scroll-text-2-v1",
        entry_point="miniwob.envs.miniwob_envs:ScrollText2Env",
    )
    register(
        id="miniwob/search-engine-v1",
        entry_point="miniwob.envs.miniwob_envs:SearchEngineEnv",
    )
    register(
        id="miniwob/simple-algebra-v1",
        entry_point="miniwob.envs.miniwob_envs:SimpleAlgebraEnv",
    )
    register(
        id="miniwob/simple-arithmetic-v1",
        entry_point="miniwob.envs.miniwob_envs:SimpleArithmeticEnv",
    )
    register(
        id="miniwob/social-media-v1",
        entry_point="miniwob.envs.miniwob_envs:SocialMediaEnv",
    )
    register(
        id="miniwob/social-media-all-v1",
        entry_point="miniwob.envs.miniwob_envs:SocialMediaAllEnv",
    )
    register(
        id="miniwob/social-media-some-v1",
        entry_point="miniwob.envs.miniwob_envs:SocialMediaSomeEnv",
    )
    register(
        id="miniwob/terminal-v1",
        entry_point="miniwob.envs.miniwob_envs:TerminalEnv",
        nondeterministic=True,
    )
    register(
        id="miniwob/text-editor-v1",
        entry_point="miniwob.envs.miniwob_envs:TextEditorEnv",
    )
    register(
        id="miniwob/text-transform-v1",
        entry_point="miniwob.envs.miniwob_envs:TextTransformEnv",
    )
    register(
        id="miniwob/tic-tac-toe-v1",
        entry_point="miniwob.envs.miniwob_envs:TicTacToeEnv",
    )
    register(
        id="miniwob/unicode-test-v1",
        entry_point="miniwob.envs.miniwob_envs:UnicodeTestEnv",
    )
    register(
        id="miniwob/use-autocomplete-v1",
        entry_point="miniwob.envs.miniwob_envs:UseAutocompleteEnv",
    )
    register(
        id="miniwob/use-autocomplete-nodelay-v1",
        entry_point="miniwob.envs.miniwob_envs:UseAutocompleteNodelayEnv",
    )
    register(
        id="miniwob/use-colorwheel-v1",
        entry_point="miniwob.envs.miniwob_envs:UseColorwheelEnv",
    )
    register(
        id="miniwob/use-colorwheel-2-v1",
        entry_point="miniwob.envs.miniwob_envs:UseColorwheel2Env",
    )
    register(
        id="miniwob/use-slider-v1",
        entry_point="miniwob.envs.miniwob_envs:UseSliderEnv",
    )
    register(
        id="miniwob/use-slider-2-v1",
        entry_point="miniwob.envs.miniwob_envs:UseSlider2Env",
    )
    register(
        id="miniwob/use-spinner-v1",
        entry_point="miniwob.envs.miniwob_envs:UseSpinnerEnv",
    )
    register(
        id="miniwob/visual-addition-v1",
        entry_point="miniwob.envs.miniwob_envs:VisualAdditionEnv",
    )
    # FlightWoB tasks
    register(
        id="miniwob/flight.Alaska-v1",
        entry_point="miniwob.envs.flightwob_envs:FlightAlaskaEnv",
    )
    register(
        id="miniwob/flight.Alaska-auto-v1",
        entry_point="miniwob.envs.flightwob_envs:FlightAlaskaAutoEnv",
    )
    register(
        id="miniwob/flight.AA-v1",
        entry_point="miniwob.envs.flightwob_envs:FlightAAEnv",
    )
    # MiniWoB test set
    register(
        id="miniwob/ascending-numbers-v1",
        entry_point="miniwob.envs.miniwob_envs:AscendingNumbersEnv",
    )
    register(
        id="miniwob/buy-ticket-v1",
        entry_point="miniwob.envs.miniwob_envs:BuyTicketEnv",
    )
    register(
        id="miniwob/daily-calendar-v1",
        entry_point="miniwob.envs.miniwob_envs:DailyCalendarEnv",
    )
    register(
        id="miniwob/drag-shapes-2-v1",
        entry_point="miniwob.envs.miniwob_envs:DragShapes2Env",
    )
    register(
        id="miniwob/drag-single-shape-v1",
        entry_point="miniwob.envs.miniwob_envs:DragSingleShapeEnv",
    )
    register(
        id="miniwob/draw-circle-v1",
        entry_point="miniwob.envs.miniwob_envs:DrawCircleEnv",
    )
    register(
        id="miniwob/draw-line-v1",
        entry_point="miniwob.envs.miniwob_envs:DrawLineEnv",
    )
    register(
        id="miniwob/find-greatest-v1",
        entry_point="miniwob.envs.miniwob_envs:FindGreatestEnv",
    )
    register(
        id="miniwob/form-sequence-v1",
        entry_point="miniwob.envs.miniwob_envs:FormSequenceEnv",
    )
    register(
        id="miniwob/form-sequence-2-v1",
        entry_point="miniwob.envs.miniwob_envs:FormSequence2Env",
    )
    register(
        id="miniwob/form-sequence-3-v1",
        entry_point="miniwob.envs.miniwob_envs:FormSequence3Env",
    )
    register(
        id="miniwob/generate-number-v1",
        entry_point="miniwob.envs.miniwob_envs:GenerateNumberEnv",
    )
    register(
        id="miniwob/hot-cold-v1",
        entry_point="miniwob.envs.miniwob_envs:HotColdEnv",
    )
    register(
        id="miniwob/odd-or-even-v1",
        entry_point="miniwob.envs.miniwob_envs:OddOrEvenEnv",
    )
    register(
        id="miniwob/order-food-v1",
        entry_point="miniwob.envs.miniwob_envs:OrderFoodEnv",
    )
    register(
        id="miniwob/phone-book-v1",
        entry_point="miniwob.envs.miniwob_envs:PhoneBookEnv",
    )
    register(
        id="miniwob/sign-agreement-v1",
        entry_point="miniwob.envs.miniwob_envs:SignAgreementEnv",
    )
    register(
        id="miniwob/stock-market-v1",
        entry_point="miniwob.envs.miniwob_envs:StockMarketEnv",
        nondeterministic=True,
    )
