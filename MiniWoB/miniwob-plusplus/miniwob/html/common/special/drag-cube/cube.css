.viewport {
  -webkit-perspective: 800px;
  -moz-perspective: 800px;
  -ms-perspective: 800px;
  -o-perspective: 800px;
  perspective: 800px;
  -webkit-perspective-origin: 50% 80px;
  -moz-perspective-origin: 50% 80px;
  -ms-perspective-origin: 50% 80px;
  -o-perspective-origin: 50% 80px;
  perspective-origin: 50% 80px;
  -webkit-transform: scale(0.8, 0.8);
  -moz-transform: scale(0.8, 0.8);
  -ms-transform: scale(0.8, 0.8);
  -o-transform: scale(0.8, 0.8);
  transform: scale(0.8, 0.8);
}

.cube {
  position: relative;
  margin: 20px auto;
  height: 80px;
  width: 80px;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform: rotateX(136deg) rotateY(1122deg);
  -moz-transform: rotateX(136deg) rotateY(1122deg);
  -ms-transform: rotateX(136deg) rotateY(1122deg);
  -o-transform: rotateX(136deg) rotateY(1122deg);
  transform: rotateX(136deg) rotateY(1122deg); }

.cube > div {
  overflow: hidden;
  position: absolute;
  opacity: 0.9;
  height: 80px;
  width: 80px;
  background-image: url("blank.png");
  -webkit-touch-callout: none;
  -moz-touch-callout: none;
  -ms-touch-callout: none;
  -o-touch-callout: none;
  touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none; }

.cube > div > div.cube-image {
  width: 80px;
  height: 80px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  line-height: 80px;
  font-size: 80px;
  text-align: center;
  color: #1b9bd8;
  -webkit-transition: color 600ms;
  -moz-transition: color 600ms;
  -ms-transition: color 600ms;
  -o-transition: color 600ms;
  transition: color 600ms; }
  .cube > div > div.cube-image.active {
    color: red; }

.cube > div:hover {
  cursor: pointer; }

.cube > div:active {
  cursor: pointer; }

.cube > div:first-child {
  -webkit-transform: rotateX(90deg) translateZ(40px);
  -moz-transform: rotateX(90deg) translateZ(40px);
  -ms-transform: rotateX(90deg) translateZ(40px);
  -o-transform: rotateX(90deg) translateZ(40px);
  transform: rotateX(90deg) translateZ(40px);
  outline: 1px solid transparent; }

.cube > div:nth-child(2) {
  -webkit-transform: translateZ(40px);
  -moz-transform: translateZ(40px);
  -ms-transform: translateZ(40px);
  -o-transform: translateZ(40px);
  transform: translateZ(40px);
  outline: 1px solid transparent; }

.cube > div:nth-child(3) {
  -webkit-transform: rotateY(90deg) translateZ(40px);
  -moz-transform: rotateY(90deg) translateZ(40px);
  -ms-transform: rotateY(90deg) translateZ(40px);
  -o-transform: rotateY(90deg) translateZ(40px);
  transform: rotateY(90deg) translateZ(40px);
  outline: 1px solid transparent; }

.cube > div:nth-child(4) {
  -webkit-transform: rotateY(180deg) translateZ(40px);
  -moz-transform: rotateY(180deg) translateZ(40px);
  -ms-transform: rotateY(180deg) translateZ(40px);
  -o-transform: rotateY(180deg) translateZ(40px);
  transform: rotateY(180deg) translateZ(40px);
  outline: 1px solid transparent; }

.cube > div:nth-child(5) {
  -webkit-transform: rotateY(-90deg) translateZ(40px);
  -moz-transform: rotateY(-90deg) translateZ(40px);
  -ms-transform: rotateY(-90deg) translateZ(40px);
  -o-transform: rotateY(-90deg) translateZ(40px);
  transform: rotateY(-90deg) translateZ(40px);
  outline: 1px solid transparent; }

.cube > div:nth-child(6) {
  -webkit-transform: rotateX(-90deg) rotate(180deg) translateZ(40px);
  -moz-transform: rotateX(-90deg) rotate(180deg) translateZ(40px);
  -ms-transform: rotateX(-90deg) rotate(180deg) translateZ(40px);
  -o-transform: rotateX(-90deg) rotate(180deg) translateZ(40px);
  transform: rotateX(-90deg) rotate(180deg) translateZ(40px);
  outline: 1px solid transparent; }

object {
  opacity: 0.5; }

object:hover {
  opacity: 1;
}
