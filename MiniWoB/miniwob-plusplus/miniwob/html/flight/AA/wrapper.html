<!DOCTYPE html>
<html>
<head>
<title>AA</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../../core/core.css">
<link rel="stylesheet" type="text/css" href="../flight-common/wrapper.css">
<!-- JS -->
<script src="../../core/core.js"></script>
<script src="../flight-common/wrapper.js"></script>
<script src="dataset-AA.js"></script>

<script>
core.EPISODE_MAX_TIME = 60000;  // 1 minute
core.requiredFields = [
  "segments[0].origin",
  "segments[0].destination",
  "segments[0].travelDate",
  "segments[1].travelDate",
];

window.onload = function() {
  core.startEpisode();
  document.body.removeEventListener('click', core.canvasDrawClick);
}
</script>
</head>

<body>
<div id="query-wrap">
  <div id="query-pretty">(Instruction)</div>
  <div id="query">(Raw query)</div>
  <div id="reward-reason">(Reward reason)</div>
</div>
<iframe id="wrap" sandbox="allow-same-origin allow-scripts allow-forms"></iframe>
</body>
</html>
