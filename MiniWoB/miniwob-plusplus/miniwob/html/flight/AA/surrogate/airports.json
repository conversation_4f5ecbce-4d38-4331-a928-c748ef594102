[{"name": "<PERSON><PERSON>", "code": "AAA", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Annaba Les Salines", "code": "AAE", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Aalborg", "code": "AAL", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Mala Mala", "code": "AAM", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Al Ain", "code": "AAN", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "<PERSON><PERSON>", "code": "AAQ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Aar<PERSON>", "code": "AAR", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Altay", "code": "AAT", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Ara<PERSON>", "code": "AAX", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Al Ghaydah", "code": "AAY", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "Abakan", "code": "ABA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Albacete Los Llanos", "code": "ABC", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Abadan", "code": "ABD", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Allentown Bethlehem Easton Lehigh Valley Intl", "code": "ABE", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Abilene Regional Airport", "code": "ABI", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Abidjan <PERSON>", "code": "ABJ", "stateCode": "", "countryCode": "CI", "countryName": "Ivory Coast"}, {"name": "<PERSON><PERSON>", "code": "ABK", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Ambler", "code": "ABL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Bamaga", "code": "ABM", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Albuquerque International", "code": "ABQ", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Aberdeen Municipal", "code": "ABR", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "Abu <PERSON>", "code": "ABS", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Al Baha Al Aqiq", "code": "ABT", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Abuja International", "code": "ABV", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Albury", "code": "ABX", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Albany Dougherty County", "code": "ABY", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Aberdeen Dyce", "code": "ABZ", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Acapulco Juan Alvarez International", "code": "ACA", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Accra Kotoka", "code": "ACC", "stateCode": "", "countryCode": "GH", "countryName": "Ghana"}, {"name": "Lanzarote", "code": "ACE", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Altenrhein", "code": "ACH", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Alderney The Blaye", "code": "ACI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nantucket Memorial", "code": "ACK", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Sahand", "code": "ACP", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Achinsk", "code": "ACS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Waco Metropolitan Area", "code": "ACT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Eureka Arcata", "code": "ACV", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Atlantic City Intl", "code": "ACY", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "P Zabol A", "code": "ACZ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "ADA", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON> Menderes Arpt", "code": "ADB", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "ADD", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Aden International", "code": "ADE", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ADF", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Amman Civil   Marka Airport", "code": "ADJ", "stateCode": "", "countryCode": "JO", "countryName": "Jordan"}, {"name": "Adak Island NS", "code": "ADK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Adelaide", "code": "ADL", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Kodiak Airport", "code": "ADQ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ardabil", "code": "ADU", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "St Andrews Leuchars", "code": "ADX", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "San Andres Island", "code": "ADZ", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON>", "code": "AEH", "stateCode": "", "countryCode": "TD", "countryName": "Chad"}, {"name": "Buenos Aires Arpt <PERSON>", "code": "AEP", "stateCode": "BA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Sochi International Airport", "code": "AER", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Aalesund Vigra", "code": "AES", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Allakaket", "code": "AET", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Alexandria Internation", "code": "AEX", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AEY", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "San Rafael", "code": "AFA", "stateCode": "MD", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Alta Floresta", "code": "AFL", "stateCode": "MT", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Afutara Aerodrome", "code": "AFT", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AGA", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Agen La Garenne", "code": "AGF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Helsingborg Angelholm", "code": "AGH", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Tasiilaq", "code": "AGM", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Angoon", "code": "AGN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Malaga", "code": "AGP", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Agra Kheria", "code": "AGR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Augusta Bush Field", "code": "AGS", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Ciudad del Este Alejo Garcia", "code": "AGT", "stateCode": "", "countryCode": "PY", "countryName": "Paraguay"}, {"name": "Aguascalientes", "code": "AGU", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Acarigua", "code": "AGV", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Agatti Island", "code": "AGX", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "AHB", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON> Amedee AAF", "code": "AHC", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Ahe Airport", "code": "AHE", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Alghero Fertilia", "code": "AHO", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AHS", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "Al Hoceima Charif Al Idrissi", "code": "AHU", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Alliance", "code": "AIA", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AIN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AIT", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "Atiu Island", "code": "AIU", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "Atlantic City Bader Field", "code": "AIY", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "Ajaccio Campo Dell Oro", "code": "AJA", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "AJF", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Agri", "code": "AJI", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Aizawl", "code": "AJL", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Anjouan Ouani", "code": "AJN", "stateCode": "", "countryCode": "KM", "countryName": "Comoros"}, {"name": "Sabre Tech Off line Point", "code": "AJP", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "AJR", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Aracaju", "code": "AJU", "stateCode": "SE", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Ankan<PERSON>", "code": "AKA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Atka", "code": "AKB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AKF", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AKI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AKJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Akhiok SPB", "code": "AKK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Auckland <PERSON>", "code": "AKL", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "King Salmon", "code": "AKN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Anaktuvuk", "code": "AKP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "AKS", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AKU", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Akulivik", "code": "AKV", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Aktyubinsk", "code": "AKX", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Sittwe Civil", "code": "AKY", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Almaty", "code": "ALA", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Albany International", "code": "ALB", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Alicante", "code": "ALC", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Alta", "code": "ALF", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Algiers Houari Boumediene", "code": "ALG", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Albany", "code": "ALH", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Alamogordo Municipal", "code": "ALM", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Waterloo", "code": "ALO", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Alamosa Municipal", "code": "ALS", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Wall<PERSON> Wall<PERSON>", "code": "ALW", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Alexandria El Nohza", "code": "ALY", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Alitak SPB", "code": "ALZ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Amarillo International", "code": "AMA", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Ahmedabad", "code": "AMD", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AMH", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "<PERSON><PERSON>", "code": "AMI", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Amman Queen <PERSON><PERSON>", "code": "AMM", "stateCode": "", "countryCode": "JO", "countryName": "Jordan"}, {"name": "<PERSON><PERSON>", "code": "AMQ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Amsterdam Schiphol", "code": "AMS", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Amderma", "code": "AMV", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Ambatomainty", "code": "AMY", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Anchorage International", "code": "ANC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "ANE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "ANF", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ANI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ankara Etimesgut", "code": "ANK", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ANM", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Antwerp Deurne", "code": "ANR", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ANS", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Antigua VC Bird Intl", "code": "ANU", "stateCode": "", "countryCode": "AG", "countryName": "Antigua And Barbuda"}, {"name": "Anvik", "code": "ANV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Andenes", "code": "ANX", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Eskisehir <PERSON> University", "code": "AOE", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Ancona Falconara", "code": "AOI", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AOJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AOK", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Altoona Martinsburg", "code": "AOO", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "AOR", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON>", "code": "AOS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Denver Arapahoe Co", "code": "APA", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Naples", "code": "APF", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Apataki", "code": "APK", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Nampula", "code": "APL", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Alpena County Regional", "code": "APN", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Apartado", "code": "APO", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Apia Faleolo", "code": "APW", "stateCode": "", "countryCode": "WS", "countryName": "Samoa"}, {"name": "Anqing", "code": "AQG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AQI", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Aqaba King <PERSON>", "code": "AQJ", "stateCode": "", "countryCode": "JO", "countryName": "Jordan"}, {"name": "Arequipa <PERSON>", "code": "AQP", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "New Iberia Acadiana Regional", "code": "ARA", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "Arctic Village", "code": "ARC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Arkhangelsk", "code": "ARH", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Arica Chacalluta", "code": "ARI", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ARK", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Armidale", "code": "ARM", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Stockholm Arlanda", "code": "ARN", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Watertown", "code": "ART", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Aracatuba", "code": "ARU", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Min<PERSON><PERSON> F <PERSON>", "code": "ARV", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Arad", "code": "ARW", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ASA", "stateCode": "", "countryCode": "ER", "countryName": "Eritrea"}, {"name": "Ashgabat", "code": "ASB", "stateCode": "", "countryCode": "TM", "countryName": "Turkmenistan"}, {"name": "Andros Town", "code": "ASD", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Aspen", "code": "ASE", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Astrakhan", "code": "ASF", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Georgetown Wideawake Fld", "code": "ASI", "stateCode": "", "countryCode": "SH", "countryName": "Saint Helena"}, {"name": "<PERSON><PERSON>", "code": "ASJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Asmara International", "code": "ASM", "stateCode": "", "countryCode": "ER", "countryName": "Eritrea"}, {"name": "<PERSON><PERSON>", "code": "ASO", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Alice Springs", "code": "ASP", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ASR", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Asuncion <PERSON><PERSON>", "code": "ASU", "stateCode": "", "countryCode": "PY", "countryName": "Paraguay"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ASV", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON>", "code": "ASW", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Arthurs Town", "code": "ATC", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "At<PERSON><PERSON>", "code": "ATD", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Athens Eleftherios <PERSON>", "code": "ATH", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Atqasuk", "code": "ATK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Atlanta Hartsfield Jackson", "code": "ATL", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Altamira", "code": "ATM", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Amritsar Raja Sansi", "code": "ATQ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Atar Mouakchott", "code": "ATR", "stateCode": "", "countryCode": "MR", "countryName": "Mauritania"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "ATT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Appleton International Airport", "code": "ATW", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Watertown", "code": "ATY", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ATZ", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Aruba Reina Beatrix", "code": "AUA", "stateCode": "", "countryCode": "AW", "countryName": "Aruba"}, {"name": "Arauca", "code": "AUC", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Augusta", "code": "AUG", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Abu Dhabi Intl", "code": "AUH", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AUK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>uo<PERSON>", "code": "AUQ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Aurillac", "code": "AUR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON>strom Intl", "code": "AUS", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Aurukun Mission", "code": "AUU", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AUW", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Araguaina", "code": "AUX", "stateCode": "TO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Aneityum", "code": "AUY", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Asheville Asheville Regional Hendersonville", "code": "AVL", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Avignon Caum", "code": "AVN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Scranton Wilkes Barre International", "code": "AVP", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Avu Avu", "code": "AVU", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Melbourne Avalon", "code": "AVV", "stateCode": "VI", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AWB", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AWD", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AWZ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AXA", "stateCode": "", "countryCode": "AI", "countryName": "<PERSON><PERSON><PERSON>"}, {"name": "Alexandroupolis Demokritos Airport", "code": "AXD", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Armenia El Eden", "code": "AXM", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Spring Point Springpoint Arpt", "code": "AXP", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "AXR", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AXT", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Axum", "code": "AXU", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "<PERSON>ya<PERSON><PERSON>", "code": "AYP", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Ayers <PERSON>", "code": "AYQ", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AYT", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Yazd", "code": "AZD", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AZN", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Kalamazoo Battle Creek Intl", "code": "AZO", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "AZR", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Samana International", "code": "AZS", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BAG", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Bahrain International", "code": "BAH", "stateCode": "", "countryCode": "BH", "countryName": "Bahrain"}, {"name": "<PERSON>", "code": "BAL", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Barranquilla E Cortissoz", "code": "BAQ", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BAS", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Bauru", "code": "BAU", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BAV", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Barnaul", "code": "BAX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Baia Mare", "code": "BAY", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Balmaceda Teniente Vidal", "code": "BBA", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "BBI", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BBK", "stateCode": "", "countryCode": "BW", "countryName": "Botswana"}, {"name": "Bario", "code": "BBN", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Berber<PERSON>", "code": "BBO", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "Blackbush", "code": "BBS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Bucharest Baneasa", "code": "BBU", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Baracoa", "code": "BCA", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Bacolod", "code": "BCD", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BCI", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Barra Colorado", "code": "BCL", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Bacau", "code": "BCM", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Barcelona", "code": "BCN", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Bermuda Bermuda International Hamilton", "code": "BDA", "stateCode": "", "countryCode": "BM", "countryName": "Bermuda"}, {"name": "Bundaberg", "code": "BDB", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Badu Island", "code": "BDD", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bandar Lengeh", "code": "BDH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BDJ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Hartford Springfield Windsor Locks Bradley International", "code": "BDL", "stateCode": "CT", "countryCode": "US", "countryName": "United States"}, {"name": "Bandung Husein Sastranegara", "code": "BDO", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BDP", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Vadodara", "code": "BDQ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Bridgeport Igor I Sikorsky Mem", "code": "BDR", "stateCode": "CT", "countryCode": "US", "countryName": "United States"}, {"name": "Brindisi Papola <PERSON>", "code": "BDS", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Benbe<PERSON>", "code": "BEB", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Belgrade Beograd", "code": "BEG", "stateCode": "", "countryCode": "RS", "countryName": "Serbia"}, {"name": "Belem Val De Cans", "code": "BEL", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Benghazi Benina Intl", "code": "BEN", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "<PERSON><PERSON>", "code": "BEP", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Berlin", "code": "BER", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Brest Guipavas", "code": "BES", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Bethel Airport", "code": "BET", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Bed<PERSON><PERSON>", "code": "BEU", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "BEW", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Beirut International", "code": "BEY", "stateCode": "", "countryCode": "LB", "countryName": "Lebanon"}, {"name": "Bradford", "code": "BFD", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Bielefeld", "code": "BFE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Scotts Bluff County", "code": "BFF", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "Seattle Boeing Fld Intl", "code": "BFI", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Bakersfield Meadows Field", "code": "BFL", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Bloemfontein Intl", "code": "BFN", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Belfast Intl Arpt", "code": "BFS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "BFV", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Bucaramanga Palo Negro", "code": "BGA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON>", "code": "BGF", "stateCode": "", "countryCode": "CF", "countryName": "Central African Republic"}, {"name": "Bridgetown Grantley Adams Intl", "code": "BGI", "stateCode": "", "countryCode": "BB", "countryName": "Barbados"}, {"name": "Binghamton Endicott Johnson City", "code": "BGM", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Bergen Flesland", "code": "BGO", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Bangor International", "code": "BGR", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Baghdad Al Muthana", "code": "BGW", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "Milan Orio Al Serio", "code": "BGY", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Bar Harbor", "code": "BHB", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Belfast City Airport", "code": "BHD", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Blenheim", "code": "BHE", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Brus Laguna", "code": "BHG", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BHH", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Bahia Blanca Comandante", "code": "BHI", "stateCode": "BA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BHJ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Bukhara", "code": "BHK", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Birmingham", "code": "BHM", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "Bhopal", "code": "BHO", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Broken Hill", "code": "BHQ", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bharatpur", "code": "BHR", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Bathurst Raglan", "code": "BHS", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bhavnagar", "code": "BHU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Bahawalpur", "code": "BHV", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Birmingham", "code": "BHX", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Beihai", "code": "BHY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Belo Horizonte", "code": "BHZ", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Bast<PERSON>", "code": "BIA", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Block Island", "code": "BID", "stateCode": "RI", "countryCode": "US", "countryName": "United States"}, {"name": "Bikini Atoll Enyu Airfield", "code": "BII", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "Biak <PERSON>", "code": "BIK", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON>", "code": "BIL", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Bimini International", "code": "BIM", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Bilbao", "code": "BIO", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BIQ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Biratnagar", "code": "BIR", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Bismarck", "code": "BIS", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "Bejaia", "code": "BJA", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Bojnord", "code": "BJB", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Batsfjord", "code": "BJF", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BJI", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Banjul Yundum International", "code": "BJL", "stateCode": "", "countryCode": "GM", "countryName": "Gambia"}, {"name": "Bujumbura International", "code": "BJM", "stateCode": "", "countryCode": "BI", "countryName": "Burundi"}, {"name": "Bahar Dar", "code": "BJR", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Beijing", "code": "BJS", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Bodrum Milas Airport", "code": "BJV", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Guanajuato Leon Del Bajio", "code": "BJX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Badajoz Talaveral La Real", "code": "BJZ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Moscow Bykovo", "code": "BKA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "BKC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kota Kinabalu", "code": "BKI", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Bangkok International", "code": "BKK", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Cleveland Burke Lakefront", "code": "BKL", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Bakalalan", "code": "BKM", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Ba<PERSON><PERSON>", "code": "BKO", "stateCode": "", "countryCode": "ML", "countryName": "Mali"}, {"name": "<PERSON><PERSON>", "code": "BKQ", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON>g<PERSON><PERSON>", "code": "BKS", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Beckley", "code": "BKW", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "Bukavu Kamenbe", "code": "BKY", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Barcelona Gen J A Anzoategui", "code": "BLA", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Boulder City", "code": "BLD", "stateCode": "NV", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BLE", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Bellingham", "code": "BLI", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Batna", "code": "BLJ", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Blackpool", "code": "BLK", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "BLL", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Bologna Guglielmo <PERSON>", "code": "BLQ", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Bangalore Hindustan", "code": "BLR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Blackwater", "code": "BLT", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Belleville", "code": "BLV", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Blantyre Chileka", "code": "BLZ", "stateCode": "", "countryCode": "MW", "countryName": "Malawi"}, {"name": "Stockholm Bromma", "code": "BMA", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BME", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bloomington Normal", "code": "BMI", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Borkum", "code": "BMK", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BMO", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Bima", "code": "BMU", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Banmethuot Phung Duc", "code": "BMV", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BMW", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Belep Island", "code": "BMY", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "Nashville International", "code": "BNA", "stateCode": "TN", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BND", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Brisbane International", "code": "BNE", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Benin City", "code": "BNI", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Cologne Bonn   Off line Point", "code": "BNJ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Ballina Byron Gateway", "code": "BNK", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bronnoysund Bronnoy", "code": "BNN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON>", "code": "BNS", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Banja Lu<PERSON>", "code": "BNX", "stateCode": "", "countryCode": "BA", "countryName": "Bosnia And Herzegovina"}, {"name": "Bellona", "code": "BNY", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON>", "code": "BOB", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Bocas Del Toro", "code": "BOC", "stateCode": "", "countryCode": "PA", "countryName": "Panama"}, {"name": "Bordeaux Airport", "code": "BOD", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Bogota Eldorado", "code": "BOG", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Bournemouth International", "code": "BOH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Boise Air Term Gowen Fld", "code": "BOI", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Bourgas", "code": "BOJ", "stateCode": "", "countryCode": "BG", "countryName": "Bulgaria"}, {"name": "Mumbai Chhatrapati Shivaji", "code": "BOM", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Bonaire Flamingo International", "code": "BON", "stateCode": "", "countryCode": "BQ", "countryName": "Bes Islands"}, {"name": "Bodo", "code": "BOO", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Boston Logan International", "code": "BOS", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BOW", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BOY", "stateCode": "", "countryCode": "BF", "countryName": "Burkina Faso"}, {"name": "Balikpa<PERSON>", "code": "BPN", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Porto Seguro", "code": "BPS", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Beaumont Jefferson County", "code": "BPT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Bangda", "code": "BPX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Besalampy", "code": "BPY", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Brunswick Glynco Jetport", "code": "BQK", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BQL", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Aguadilla <PERSON>", "code": "BQN", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Blagoveschensk", "code": "BQS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Barreiras", "code": "BRA", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "San Carlos De Bariloche International", "code": "BRC", "stateCode": "RN", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON> County", "code": "BRD", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Bremen", "code": "BRE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Bradford", "code": "BRF", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Bari Palese", "code": "BRI", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BRK", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Burlington", "code": "BRL", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Barquisimeto", "code": "BRM", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Berne Belp", "code": "BRN", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Brownsville South Padre Is Intl", "code": "BRO", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BRQ", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "Barra North Bay", "code": "BRR", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Bristol", "code": "BRS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Brussels National", "code": "BRU", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "Bremerhaven", "code": "BRV", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Barrow WRogers M <PERSON>", "code": "BRW", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BSA", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "Brasilia International", "code": "BSB", "stateCode": "DF", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Bahia <PERSON>ano", "code": "BSC", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BSD", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "BSG", "stateCode": "", "countryCode": "GQ", "countryName": "Equatorial Guinea"}, {"name": "Brighton", "code": "BSH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Biskra", "code": "BSK", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Basel Mulhouse EuroAirport Swiss", "code": "BSL", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Basco", "code": "BSO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Basra International", "code": "BSR", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "<PERSON><PERSON>", "code": "BSX", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON>", "code": "BTH", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Barter Island", "code": "BTI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Banda Aceh Blang Bintang", "code": "BTJ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Bratsk", "code": "BTK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Battle Creek WK Kellogg Regional", "code": "BTL", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Butte", "code": "BTM", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Baton Rouge Ryan", "code": "BTR", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "Bratislava Ivanka", "code": "BTS", "stateCode": "", "countryCode": "SK", "countryName": "Slovakia"}, {"name": "Bettles", "code": "BTT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Bintulu", "code": "BTU", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Burlington International", "code": "BTV", "stateCode": "VT", "countryCode": "US", "countryName": "United States"}, {"name": "Bursa Airport", "code": "BTZ", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "BUA", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Burketown", "code": "BUC", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Budapest Ferihegy", "code": "BUD", "stateCode": "", "countryCode": "HU", "countryName": "Hungary"}, {"name": "Buenos Aires", "code": "BUE", "stateCode": "BA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Buffalo Niagara Intl", "code": "BUF", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Bucharest", "code": "BUH", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Buenaventura", "code": "BUN", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BUO", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "Bulawayo", "code": "BUQ", "stateCode": "", "countryCode": "ZW", "countryName": "Zimbabwe"}, {"name": "Hollywood Burbank Airport", "code": "BUR", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BUS", "stateCode": "", "countryCode": "GE", "countryName": "Georgia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BUX", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BUZ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Paris Beauvais Tille Beauvais Tille Airport", "code": "BVA", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Boa Vista", "code": "BVB", "stateCode": "RR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Boa Vista Rabil", "code": "BVC", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "Brive La Gaillarde <PERSON>", "code": "BVE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Berlevag", "code": "BVG", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BVH", "stateCode": "RO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Birdsville", "code": "BVI", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "BWA", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Braunschweig", "code": "BWE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Barrow In Furness Walney Island", "code": "BWF", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Baltimore Washington International Airport", "code": "BWI", "stateCode": "MD", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "BWK", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Bandar Seri Begawan Brunei International", "code": "BWN", "stateCode": "", "countryCode": "BN", "countryName": "Brunei"}, {"name": "<PERSON><PERSON>", "code": "BWT", "stateCode": "TS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Bam", "code": "BXR", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "BXU", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Bayamo CM de Cespedes", "code": "BYM", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "BYN", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "Belize City Philip <PERSON>son Int", "code": "BZE", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "Bydgoszcz", "code": "BZG", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Briansk", "code": "BZK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Barisal", "code": "BZL", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "Bozeman Gallatin Field", "code": "BZN", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Bolzano", "code": "BZO", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Beziers Vias", "code": "BZR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Brazzaville Maya Maya", "code": "BZV", "stateCode": "", "countryCode": "CG", "countryName": "Congo (brazzaville)"}, {"name": "Brize Norton RAF Station", "code": "BZZ", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Cabinda", "code": "CAB", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Cascavel", "code": "CAC", "stateCode": "PR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Columbia Metropolitan Airport", "code": "CAE", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "Cag<PERSON><PERSON>", "code": "CAG", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Ca Mau", "code": "CAH", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Cairo International", "code": "CAI", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Akron Canton Regional", "code": "CAK", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Campbeltown Machrihanish", "code": "CAL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Guangzhou Baiyun", "code": "CAN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Cap <PERSON>en", "code": "CAP", "stateCode": "", "countryCode": "HT", "countryName": "Haiti"}, {"name": "Casablanca Anfa", "code": "CAS", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Carlisle", "code": "CAX", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CAY", "stateCode": "", "countryCode": "GF", "countryName": "French Guiana"}, {"name": "Cobar", "code": "CAZ", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Cochabamba J Wilsterman", "code": "CBB", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Cambridge", "code": "CBG", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CBH", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Cotabato Awang", "code": "CBO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Calabar", "code": "CBQ", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Canberra", "code": "CBR", "stateCode": "AC", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "CBT", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Cayo Coco", "code": "CCC", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Kozhikode", "code": "CCJ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Cocos Islands", "code": "CCK", "stateCode": "", "countryCode": "CC", "countryName": "Cocos (keeling) Islands"}, {"name": "Cricium<PERSON>", "code": "CCM", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Concepcion Carriel Sur", "code": "CCP", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Caracas <PERSON>", "code": "CCS", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Kolkata Net<PERSON><PERSON>", "code": "CCU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Craig Cove", "code": "CCV", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON>", "code": "CCZ", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Cold Bay", "code": "CDB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Cedar City", "code": "CDC", "stateCode": "UT", "countryCode": "US", "countryName": "United States"}, {"name": "Paris Ch De Gaulle", "code": "CDG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "CDR", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CDV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "CDW", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "Cebu Mactan International", "code": "CEB", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Crescent City Mc Namara Fld", "code": "CEC", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CED", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Cherepovets", "code": "CEE", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Chester", "code": "CEG", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON>", "code": "CEI", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Chelyabinsk", "code": "CEK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Central", "code": "CEM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ciudad Obregon", "code": "CEN", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Cortez Montezuma County", "code": "CEZ", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Cacador", "code": "CFC", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Clermont Ferrand <PERSON>", "code": "CFE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Cienfuegos", "code": "CFG", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CFK", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Donegal", "code": "CFN", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "<PERSON><PERSON>", "code": "CFR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Coffs Harbour", "code": "CFS", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Kerkyra I Kapodistrias", "code": "CFU", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Craig <PERSON>", "code": "CGA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CGB", "stateCode": "MT", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "CGD", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Sao Paulo Congonhas", "code": "CGH", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Cape Girardeau", "code": "CGI", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "Jakarta Soekarno Hatta Intl", "code": "CGK", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Camiguin <PERSON>", "code": "CGM", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Bonn Cologne", "code": "CGN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Zhengzhou", "code": "CGO", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Chittagong Patenga", "code": "CGP", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CGQ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Campo Grande Internacional", "code": "CGR", "stateCode": "MS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Cagayan De Oro Lumbia", "code": "CGY", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Chattanooga Lovell Field", "code": "CHA", "stateCode": "TN", "countryCode": "US", "countryName": "United States"}, {"name": "Christchurch International", "code": "CHC", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Chicago", "code": "CHI", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Charlottesville Albemarle", "code": "CHO", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "CHQ", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Charleston AFB International Airport", "code": "CHS", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "Chatham Island Karewa", "code": "CHT", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "CHU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Choiseul Bay", "code": "CHY", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Rome Ciampino", "code": "CIA", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Chico", "code": "CIC", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Cedar Rapids", "code": "CID", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "CIF", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Changzhi", "code": "CIH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Cobija E Beltram", "code": "CIJ", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Chalkyitsik", "code": "CIK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Chipata", "code": "CIP", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "Shimkent", "code": "CIT", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Sault Ste Marie Chippewa County", "code": "CIU", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Canouan Island", "code": "CIW", "stateCode": "", "countryCode": "VC", "countryName": "Saint Vincent And The Grenadines"}, {"name": "Chiclay<PERSON>", "code": "CIX", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Cajamarca", "code": "CJA", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Coimbatore <PERSON>", "code": "CJB", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Calama El Loa", "code": "CJC", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Cheongju", "code": "CJJ", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Chitral", "code": "CJL", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Ciudad Juarez Intl Abraham Gonzalez", "code": "CJS", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Jeju Airport", "code": "CJU", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Clarksburg Benedum", "code": "CKB", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "Crooked Creek", "code": "CKD", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Chongqing", "code": "CKG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "CKH", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "CKS", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Chicken", "code": "CKX", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Conakry", "code": "CKY", "stateCode": "", "countryCode": "GN", "countryName": "Guinea"}, {"name": "Canakkale", "code": "CKZ", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "San Diego Carlsbad", "code": "CLD", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Cleveland Hopkins International", "code": "CLE", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CLJ", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "College Station Easterwood Field", "code": "CLL", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Port Angeles Fairchild Intl", "code": "CLM", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Cali Alfonso B Aragon", "code": "CLO", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Clarks Point", "code": "CLP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Colima", "code": "CLQ", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Charlotte Douglas International Airport", "code": "CLT", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Clearwater Executive", "code": "CLW", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>vi Ste Catherine", "code": "CLY", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "CMA", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Colombo Bandaranayake Intl", "code": "CMB", "stateCode": "", "countryCode": "LK", "countryName": "Sri Lanka"}, {"name": "Ciudad Del Carmen", "code": "CME", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "CMF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Corumba Internacional", "code": "CMG", "stateCode": "MS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON>", "code": "CMH", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Champaign Willard University", "code": "CMI", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Casablanca Mohamed V", "code": "CMN", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Kundiawa Chimbu", "code": "CMU", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Camaguey Ign Agramonte Intl", "code": "CMW", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Hancock Houghton County", "code": "CMX", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Coonamble", "code": "CNB", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Coconut Island", "code": "CNC", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CND", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Belo Horizonte Tancredo Neves Intl", "code": "CNF", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Cloncurry", "code": "CNJ", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Carlsbad", "code": "CNM", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Neerlerit Inaat", "code": "CNP", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Corrientes Camba Punta", "code": "CNQ", "stateCode": "CR", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Cairns", "code": "CNS", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Chiang Mai International", "code": "CNX", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Moab Canyonlands Field", "code": "CNY", "stateCode": "UT", "countryCode": "US", "countryName": "United States"}, {"name": "Cody Yellowstone Yellowstone Regional", "code": "COD", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "COK", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Coll Island", "code": "COL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Cotonou", "code": "COO", "stateCode": "", "countryCode": "BJ", "countryName": "Benin"}, {"name": "<PERSON><PERSON><PERSON>", "code": "COQ", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "Cordoba Pajas Blancas", "code": "COR", "stateCode": "CD", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Colorado Springs Municipal", "code": "COS", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Columbia Regional", "code": "COU", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "San Martin DeLos Andes Chapelco", "code": "CPC", "stateCode": "NE", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CPD", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Campeche International", "code": "CPE", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Copenhagen Airport", "code": "CPH", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Copiapo <PERSON>", "code": "CPO", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Campinas International", "code": "CPQ", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON>", "code": "CPR", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Cape Town International", "code": "CPT", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Campina Grande Joao Suassuna", "code": "CPV", "stateCode": "PB", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Culebra", "code": "CPX", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "<PERSON><PERSON>", "code": "CQD", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Calais", "code": "CQF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Craiova", "code": "CRA", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Comodoro Rivadavia", "code": "CRD", "stateCode": "CB", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Crooked Island", "code": "CRI", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Luzon Is Clark Field", "code": "CRK", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Charleroi Brussels So", "code": "CRL", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "Catarman National", "code": "CRM", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Corpus Christi International", "code": "CRP", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Charleston Yeager", "code": "CRW", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "Colonsay Is", "code": "CSA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Columbus Metropolitan", "code": "CSG", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "CSK", "stateCode": "", "countryCode": "SN", "countryName": "Senegal"}, {"name": "San Luis Obispo OSullivan AAF", "code": "CSL", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Changsha", "code": "CSX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Cheboksary", "code": "CSY", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Catania Fontanarossa", "code": "CTA", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Catamarca", "code": "CTC", "stateCode": "CA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Cartagena Rafael <PERSON>", "code": "CTG", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Charleville", "code": "CTL", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Ch<PERSON><PERSON>l", "code": "CTM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Cooktown", "code": "CTN", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Sapporo Chitose", "code": "CTS", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Chengdu", "code": "CTU", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CUC", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Cuenca", "code": "CUE", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Cune<PERSON> Levaldigi", "code": "CUF", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CUK", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "Culiacan Fedl De Bachigualato", "code": "CUL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CUM", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Cancun", "code": "CUN", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CUP", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "<PERSON><PERSON>", "code": "CUQ", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Curacao Aeropuerto Hato", "code": "CUR", "stateCode": "", "countryCode": "CW", "countryName": "Curacao"}, {"name": "Chihuahua Gen Fierro <PERSON>", "code": "CUU", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Cuzco Velazco Astete", "code": "CUZ", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Cincinnati Cinci/Nrthrn Kentucky", "code": "CVG", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Ciudad Victoria", "code": "CVM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Clovis Metropolitan Area", "code": "CVN", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Carnarvon", "code": "CVQ", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Coventry Baginton", "code": "CVT", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Corvo Island", "code": "CVU", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Wausau Central Wisconsin", "code": "CWA", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Curitiba <PERSON>", "code": "CWB", "stateCode": "PR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Chernovtsy", "code": "CWC", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Cardiff Wales Arpt", "code": "CWL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "CXB", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "Vancouver Coal Harbour", "code": "CXH", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Christmas Island", "code": "CXI", "stateCode": "", "countryCode": "KI", "countryName": "Kiribati"}, {"name": "Caxias Do Sul Campo Dos Bugres", "code": "CXJ", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Cayman Brac Is <PERSON>", "code": "CYB", "stateCode": "", "countryCode": "KY", "countryName": "Cayman Islands"}, {"name": "Chefornak SPB", "code": "CYF", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "CYI", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Cayo Largo Del Sur", "code": "CYO", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Calbayog", "code": "CYP", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Cheyenne", "code": "CYS", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Cuyo", "code": "CYU", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Cherskiy", "code": "CYX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "CYZ", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "CZE", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Corozal", "code": "CZH", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "<PERSON>", "code": "CZL", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Cozumel", "code": "CZM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Chisana Field", "code": "CZN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Cruzeiro Do Sul Campo Internacional", "code": "CZS", "stateCode": "AC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Corozal", "code": "CZU", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Changzhou", "code": "CZX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Daytona Beach International", "code": "DAB", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Dhaka Zia International", "code": "DAC", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "<PERSON>", "code": "DAD", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Dallas Love Field", "code": "DAL", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Dar Es Salaam International", "code": "DAR", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DAT", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "DAU", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON>", "code": "DAV", "stateCode": "", "countryCode": "PA", "countryName": "Panama"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DAX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dayton International Airport", "code": "DAY", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DBA", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Dubbo", "code": "DBO", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Dubuque Regional Airport", "code": "DBQ", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Dubrovnik", "code": "DBV", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Washington <PERSON>", "code": "DCA", "stateCode": "DC", "countryCode": "US", "countryName": "United States"}, {"name": "Dominica Cane Field", "code": "DCF", "stateCode": "", "countryCode": "DM", "countryName": "Dominica"}, {"name": "<PERSON><PERSON>", "code": "DCM", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Dodge City Municipal", "code": "DDC", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Dandong", "code": "DDG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "DEA", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Decatur Arpt", "code": "DEC", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Dehra Dun", "code": "DED", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Delhi Indira Gandhi Intl", "code": "DEL", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Denver International", "code": "DEN", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Dallas Fort Worth International", "code": "DFW", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "DGA", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DGE", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Dongguan", "code": "DGM", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Durango Guadalupe Victoria", "code": "DGO", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Dumaguete", "code": "DGT", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Dharamsala Gaggal Airport", "code": "DHM", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "DHN", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "Dib<PERSON><PERSON><PERSON>", "code": "DIB", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "DIE", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Diqing", "code": "DIG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON>", "code": "DIK", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "DIL", "stateCode": "", "countryCode": "TL", "countryName": "East Timor"}, {"name": "Dien Bien Phu Dien Bien", "code": "DIN", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON>re Dawa A<PERSON> Tenna <PERSON>", "code": "DIR", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DIS", "stateCode": "", "countryCode": "CG", "countryName": "Congo (brazzaville)"}, {"name": "<PERSON><PERSON>", "code": "DIU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Diyarbakir", "code": "DIY", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "DJB", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DJE", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DJG", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Jayapura Sentani", "code": "DJJ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON>", "code": "DKR", "stateCode": "", "countryCode": "SN", "countryName": "Senegal"}, {"name": "Douala", "code": "DLA", "stateCode": "", "countryCode": "CM", "countryName": "Cameroon"}, {"name": "<PERSON><PERSON>", "code": "DLC", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dillingham Municipal", "code": "DLG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Duluth International", "code": "DLH", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "DLI", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON>", "code": "DLM", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Disneyland Paris", "code": "DLP", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Dali City Dali", "code": "DLU", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dillons Bay", "code": "DLY", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Dalanzadgad", "code": "DLZ", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DMD", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Moscow Domodedovo", "code": "DME", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Dam<PERSON>m King <PERSON><PERSON><PERSON>", "code": "DMM", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Dimapur", "code": "DMU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Dundee", "code": "DND", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Dunhuang", "code": "DNH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dnepropetrovsk", "code": "DNK", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "<PERSON><PERSON>", "code": "DNR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DNZ", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Doha", "code": "DOH", "stateCode": "", "countryCode": "QA", "countryName": "Qatar"}, {"name": "Donetsk", "code": "DOK", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "<PERSON><PERSON>", "code": "DOM", "stateCode": "", "countryCode": "DM", "countryName": "Dominica"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DOP", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DOU", "stateCode": "MS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "DOY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DPL", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Devonport", "code": "DPO", "stateCode": "TS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Denpasar Bali <PERSON>", "code": "DPS", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Deering", "code": "DRG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Durango La Plata", "code": "DRO", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Dresden Arpt", "code": "DRS", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Del Rio International", "code": "DRT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "DRW", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Des Moines", "code": "DSM", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Dongsheng", "code": "DSN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dortmund", "code": "DTM", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Detroit", "code": "DTT", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Detroit Metropolitan Wayne County Airport", "code": "DTW", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Dublin", "code": "DUB", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "Dunedin", "code": "DUD", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON>", "code": "DUE", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Duisburg", "code": "DUI", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Dubois Jefferson County", "code": "DUJ", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Durban International", "code": "DUR", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Dusseldorf International", "code": "DUS", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Dutch Harbor Emergency Field", "code": "DUT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Devils Lake", "code": "DVL", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "Davao Mati", "code": "DVO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DWB", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Dubai", "code": "DWC", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "Dubai", "code": "DXB", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "<PERSON><PERSON>", "code": "DYG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "DYR", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON> dyu", "code": "DYU", "stateCode": "", "countryCode": "TJ", "countryName": "Tajikistan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "DZA", "stateCode": "", "countryCode": "YT", "countryName": "Mayotte"}, {"name": "Zhezkazgan Zhezhazgan", "code": "DZN", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Eagle", "code": "EAA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "EAE", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Kwajalein Atoll Elenak", "code": "EAL", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "EAM", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Basel Basel Mulhouse Mulhouse", "code": "EAP", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Kearney", "code": "EAR", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "San Sebastian", "code": "EAS", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Wenatchee Pangborn Field", "code": "EAT", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "EAU", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Elba Island Marina Di Campo", "code": "EBA", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "EBB", "stateCode": "", "countryCode": "UG", "countryName": "Uganda"}, {"name": "Esbjerg", "code": "EBJ", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Erbil", "code": "EBL", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "Ercan", "code": "ECN", "stateCode": "", "countryCode": "CY", "countryName": "Cyprus"}, {"name": "Northwest Florida Beaches International Airport", "code": "ECP", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Edna <PERSON>", "code": "EDA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Edinburgh", "code": "EDI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Eldoret", "code": "EDL", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON><PERSON>", "code": "EDO", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Edward River", "code": "EDR", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "EEK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kefallinia Argostolion", "code": "EFL", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON>", "code": "EGC", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Eagle Vail Eagle County", "code": "EGE", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Belgorod", "code": "EGO", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Egilsstadir", "code": "EGS", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Eagle River", "code": "EGV", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Egegik", "code": "EGX", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Eisenach", "code": "EIB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Eniseysk", "code": "EIE", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Eindhoven", "code": "EIN", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Beef Island Airport", "code": "EIS", "stateCode": "", "countryCode": "VG", "countryName": "Virgin Islands, British"}, {"name": "Barrancabermeja Variguies", "code": "EJA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "EJH", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Elko", "code": "EKO", "stateCode": "NV", "countryCode": "US", "countryName": "United States"}, {"name": "Eskilstuna", "code": "EKT", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Elcho Island", "code": "ELC", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "El Go<PERSON>", "code": "ELG", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "North Eleuthera International", "code": "ELH", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Elim", "code": "ELI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Elmira Corning Regional Airport", "code": "ELM", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "El Paso International", "code": "ELP", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Gassim", "code": "ELQ", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "East London", "code": "ELS", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "El Oued Guemar", "code": "ELU", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Elfin Cove SPB", "code": "ELV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "ELY", "stateCode": "NV", "countryCode": "US", "countryName": "United States"}, {"name": "Nottingham E Midlands", "code": "EMA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Emerald", "code": "EMD", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "EME", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Emmonak", "code": "EMK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "ENA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "ENE", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Enontekio", "code": "ENF", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON>", "code": "ENH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "El Nido", "code": "ENI", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Enschede Twente", "code": "ENS", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Enugu", "code": "ENU", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ENW", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "ENY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Medellin <PERSON>", "code": "EOH", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Elorza", "code": "EOZ", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Esperance", "code": "EPR", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "EQS", "stateCode": "CB", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Erzincan", "code": "ERC", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Erfurt", "code": "ERF", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Errachidia", "code": "ERH", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Erie International", "code": "ERI", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Erechim Comandante Kraemer", "code": "ERM", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "ERO", "code": "ERO", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Windhoek Eros", "code": "ERS", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ERZ", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Ankara Esenboga", "code": "ESB", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Escanaba Delta County Arpt", "code": "ESC", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Eastsound Orcas Island", "code": "ESD", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ESM", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "El Salvador", "code": "ESR", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Essen", "code": "ESS", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ESU", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Elat", "code": "ETH", "stateCode": "", "countryCode": "IL", "countryName": "Israel"}, {"name": "Metz Nancy Metz Nancy Lorraine", "code": "ETZ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "EUA", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Eugene", "code": "EUG", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Neumuenster", "code": "EUM", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Hassan I", "code": "EUN", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "St Eustatius F D Roosevelt", "code": "EUX", "stateCode": "", "countryCode": "BQ", "countryName": "Bes Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "EVE", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Yerevan", "code": "EVN", "stateCode": "", "countryCode": "AM", "countryName": "Armenia"}, {"name": "Evansville Dress Regional", "code": "EVV", "stateCode": "IN", "countryCode": "US", "countryName": "United States"}, {"name": "Fall River New Bedford", "code": "EWB", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "New <PERSON>", "code": "EWN", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Newark Liberty Intl", "code": "EWR", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "Excursion Inlet SPB", "code": "EXI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Exeter", "code": "EXT", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON>", "code": "EYP", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Key West International", "code": "EYW", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Buenos Aires Ministro Pistarini", "code": "EZE", "stateCode": "BA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Elazig", "code": "EZS", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Farnborough", "code": "FAB", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Faroe Islands Vagar", "code": "FAE", "stateCode": "", "countryCode": "FO", "countryName": "Faroe Islands"}, {"name": "Fairbanks International", "code": "FAI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Faro", "code": "FAO", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Fargo Hector Field", "code": "FAR", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "Fresno Airterminal", "code": "FAT", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "FAV", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Fayetteville", "code": "FAY", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "FBM", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Kalispell Glacier National Park", "code": "FCA", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Cuxhaven Nordholz", "code": "FCN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Rome Intercontinental Airport Leonardo da Vinci", "code": "FCO", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON>", "code": "FDE", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Fort De France Lamentin", "code": "FDF", "stateCode": "", "countryCode": "MQ", "countryName": "Martinique"}, {"name": "Friedrichshafen", "code": "FDH", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "FEG", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "<PERSON>", "code": "FEN", "stateCode": "FN", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "FEZ", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Fangatau", "code": "FGU", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "FHZ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Kinshasa NDjili", "code": "FIH", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Al Fujairah Fujairah Intl Arpt", "code": "FJR", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "Baden Baden Karlsruhe Soellingen", "code": "FKB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "FKI", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "<PERSON>in", "code": "FKL", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Fak Fak", "code": "FKQ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Fukushima Airport", "code": "FKS", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Florencia Capitolio", "code": "FLA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Flags<PERSON>ff P<PERSON>", "code": "FLG", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Fort Lauderdale International", "code": "FLL", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Florianopolis Hercilio Luz", "code": "FLN", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Florence", "code": "FLO", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "Florence Peretola", "code": "FLR", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Flores Island Santa Cruz", "code": "FLW", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Formosa El Pucu", "code": "FMA", "stateCode": "FO", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Farmington Municipal", "code": "FMN", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "FMO", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Freetown Lungi International", "code": "FNA", "stateCode": "", "countryCode": "SL", "countryName": "Sierra Leone"}, {"name": "Madeira", "code": "FNC", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "<PERSON><PERSON>", "code": "FNI", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Pyongyang <PERSON>", "code": "FNJ", "stateCode": "", "countryCode": "KP", "countryName": "North Korea"}, {"name": "Fort Collins Loveland Municipal Airport", "code": "FNL", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "FNT", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Fuzhou", "code": "FOC", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Fort Dodge", "code": "FOD", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Foggia Gino Lisa", "code": "FOG", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Fortaleza Pinto Martins", "code": "FOR", "stateCode": "CE", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Freeport Grand Bahama Intl", "code": "FPO", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Frankfurt International", "code": "FRA", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Franca", "code": "FRC", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Friday Harbor", "code": "FRD", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Fera Island", "code": "FRE", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON>", "code": "FRL", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Floro Flora", "code": "FRO", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Flores Santa Elena", "code": "FRS", "stateCode": "", "countryCode": "GT", "countryName": "Guatemala"}, {"name": "Bishkek", "code": "FRU", "stateCode": "", "countryCode": "KG", "countryName": "Kyrgyzstan"}, {"name": "Francistown", "code": "FRW", "stateCode": "", "countryCode": "BW", "countryName": "Botswana"}, {"name": "Figari Sud Corse", "code": "FSC", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Sioux Falls RegionalJo Foss Fld", "code": "FSD", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "Fort Smith Regional Airport", "code": "FSM", "stateCode": "AR", "countryCode": "US", "countryName": "United States"}, {"name": "St Pierre", "code": "FSP", "stateCode": "", "countryCode": "PM", "countryName": "<PERSON> And Miquelon"}, {"name": "Futuna Island Futuna Airport", "code": "FTA", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "El Calafate", "code": "FTE", "stateCode": "SC", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Fort Dauphin Marillac", "code": "FTU", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Fuerteventura Puerto del Rosario", "code": "FUE", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Fuyang", "code": "FUG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "FUJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "FUK", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Funafuti Atol International", "code": "FUN", "stateCode": "", "countryCode": "TV", "countryName": "Tuvalu"}, {"name": "Futuna Island", "code": "FUT", "stateCode": "", "countryCode": "WF", "countryName": "Wallis And <PERSON>"}, {"name": "Fort Wayne International Airport", "code": "FWA", "stateCode": "IN", "countryCode": "US", "countryName": "United States"}, {"name": "Fort William Heliport", "code": "FWM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Fort Yukon", "code": "FYU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Fayetteville Municipal Drake Fld", "code": "FYV", "stateCode": "AR", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "FZO", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON>s", "code": "GAE", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "Gafsa", "code": "GAF", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GAJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "GAL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GAM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Gan Island Gan Seenu", "code": "GAN", "stateCode": "", "countryCode": "MV", "countryName": "Maldives"}, {"name": "Guantanamo Los Canos", "code": "GAO", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "G<PERSON><PERSON><PERSON>", "code": "GAU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Gamba", "code": "GAX", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "<PERSON><PERSON>", "code": "GAY", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Great Bend", "code": "GBD", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Gaborone Sir <PERSON><PERSON><PERSON>t", "code": "GBE", "stateCode": "", "countryCode": "BW", "countryName": "Botswana"}, {"name": "<PERSON>", "code": "GBJ", "stateCode": "", "countryCode": "GP", "countryName": "Guadeloupe"}, {"name": "<PERSON><PERSON>", "code": "GBT", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Gillette Campbell County", "code": "GCC", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Guernsey", "code": "GCI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Garden City Municipal Airport", "code": "GCK", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Grand Cayman Island Owen <PERSON>tl", "code": "GCM", "stateCode": "", "countryCode": "KY", "countryName": "Cayman Islands"}, {"name": "<PERSON><PERSON>", "code": "GDE", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Guadalajara Miguel <PERSON>", "code": "GDL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Gdansk Rebiechowo", "code": "GDN", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "GDO", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Gondar", "code": "GDQ", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Grand Turk Is", "code": "GDT", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "Glendive Dawson Community", "code": "GDV", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GDX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Noumea Magenta", "code": "GEA", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "Spokane International", "code": "GEG", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "GEL", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Georgetown Cheddi Jagan <PERSON>tl", "code": "GEO", "stateCode": "", "countryCode": "GY", "countryName": "Guyana"}, {"name": "Nueva Gerona <PERSON>", "code": "GER", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON>", "code": "GES", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "GET", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Gallivare", "code": "GEV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON>", "code": "GFF", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Grand Forks", "code": "GFK", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "GFN", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Longview Gladewater Gregg County Kilgore", "code": "GGG", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "George Town Exuma International", "code": "GGT", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Glasgow International", "code": "GGW", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Ghardaia Noumerate", "code": "GHA", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Governors Harbour", "code": "GHB", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Ghat", "code": "GHT", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Gibraltar North Front", "code": "GIB", "stateCode": "", "countryCode": "GI", "countryName": "Gibraltar"}, {"name": "Boigu Island", "code": "GIC", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Rio De Janeiro Internacional", "code": "GIG", "stateCode": "RJ", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Gilgit", "code": "GIL", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Gisborne", "code": "GIS", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Jazan", "code": "GIZ", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Guanaja", "code": "GJA", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GJL", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Grand Junction Walker Field", "code": "GJT", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GKA", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Glasgow International", "code": "GLA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "GLF", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Greenville", "code": "GLH", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "Galcaio", "code": "GLK", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "GLN", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Gloucestershire", "code": "GLO", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Gladstone", "code": "GLT", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GLV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Gemena", "code": "GMA", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "GMB", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Seoul Gimpo International", "code": "GMP", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Gambier Is", "code": "GMR", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "San Sebas de la Gomera La Gomera", "code": "GMZ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Lyon Saint Geoirs", "code": "GNB", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON>", "code": "GND", "stateCode": "", "countryCode": "GD", "countryName": "Grenada"}, {"name": "Goodnews Bay", "code": "GNU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Gainesville Regional Airport", "code": "GNV", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Genoa Cristoforo Colombo", "code": "GOA", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GOH", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Goa Dabolim", "code": "GOI", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Nizhniy Novgorod", "code": "GOJ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "GOM", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Gorakhpur", "code": "GOP", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GOQ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Gothenburg Landvetter", "code": "GOT", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Garoua", "code": "GOU", "stateCode": "", "countryCode": "CM", "countryName": "Cameroon"}, {"name": "<PERSON><PERSON>", "code": "GOV", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Patras Araxos Airport", "code": "GPA", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Guapi", "code": "GPI", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Galapagos Is Baltra", "code": "GPS", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Gulfport Biloxi Regional", "code": "GPT", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "Green Bay Austin Straubel International Airport", "code": "GRB", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Grand Island", "code": "GRI", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "GRJ", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Killeen Fort Hood Regional Airport", "code": "GRK", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GRO", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Groningen Eelde", "code": "GRQ", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "<PERSON> Ford International Airport", "code": "GRR", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Sao Paulo Guarulhos Intl", "code": "GRU", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Groznyj", "code": "GRV", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Graciosa Island", "code": "GRW", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Granada", "code": "GRX", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GRY", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Graz Thalerhof", "code": "GRZ", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "Gothenburg Saeve", "code": "GSE", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GSM", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Greensboro High Point Piedmont Triad Intl", "code": "GSO", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON> Spartanburg", "code": "GSP", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "GST", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Grimsby Binbrook", "code": "GSY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Gatokae Aerodrom", "code": "GTA", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GTE", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Great Falls International", "code": "GTF", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Gorontalo Tolotio", "code": "GTO", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Columbus Golden Triangle Reg", "code": "GTR", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "Granites", "code": "GTS", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Guatemala City La Aurora", "code": "GUA", "stateCode": "", "countryCode": "GT", "countryName": "Guatemala"}, {"name": "Gunnison Crested Butte Regional Airport", "code": "GUC", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Guam AB Won <PERSON>", "code": "GUM", "stateCode": "", "countryCode": "GU", "countryName": "Guam"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GUR", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Atyrau", "code": "GUW", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Geneva Geneve Cointrin", "code": "GVA", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Governador <PERSON>", "code": "GVR", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GVX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "G<PERSON><PERSON>", "code": "GWD", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Gwalior", "code": "GWL", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Westerland   Sylt", "code": "GWT", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Galway Carnmore", "code": "GWY", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "GXF", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "Negage", "code": "GXG", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Guayaramerin", "code": "GYA", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Baku Heydar Aliyev Intl", "code": "GYD", "stateCode": "", "countryCode": "AZ", "countryName": "Azerbaijan"}, {"name": "<PERSON>lmedo International Airport", "code": "GYE", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Argyle", "code": "GYL", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Guaymas Gen <PERSON>", "code": "GYM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Goiania Santa Genoveva", "code": "GYN", "stateCode": "GO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Gizo", "code": "GZO", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Gaziantep", "code": "GZT", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "HAA", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HAC", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HAD", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HAE", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>i <PERSON> In", "code": "HAH", "stateCode": "", "countryCode": "KM", "countryName": "Comoros"}, {"name": "Hanover Arpt", "code": "HAJ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Hai<PERSON><PERSON>", "code": "HAK", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Hamburg Fuhlsbuettel", "code": "HAM", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON>", "code": "HAN", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "HAQ", "stateCode": "", "countryCode": "MV", "countryName": "Maldives"}, {"name": "Harrisburg Skyport", "code": "HAR", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>l", "code": "HAS", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Haugesund", "code": "HAU", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Havana Jose Marti Intl", "code": "HAV", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Hobart", "code": "HBA", "stateCode": "TS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Alexandria Borg El Arab", "code": "HBE", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HBT", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HBX", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HCN", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Holy Cross", "code": "HCR", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Heidelberg", "code": "HDB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Hyderabad", "code": "HDD", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Heringsdorf", "code": "HDF", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Hamadan", "code": "HDM", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Hayden Yampa Valley Steamboat Springs", "code": "HDN", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Hoedspruit Airport", "code": "HDS", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "<PERSON>", "code": "HDY", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Herat", "code": "HEA", "stateCode": "", "countryCode": "AF", "countryName": "Afghanistan"}, {"name": "<PERSON><PERSON>", "code": "HEH", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON>", "code": "HEI", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HEK", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Helsinki Vantaa", "code": "HEL", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Heraklion N Kazantzakis Apt", "code": "HER", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Hohhot", "code": "HET", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Haifa", "code": "HFA", "stateCode": "", "countryCode": "IL", "countryName": "Israel"}, {"name": "Hartford Brainard", "code": "HFD", "stateCode": "CT", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HFE", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Hammerfest", "code": "HFT", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HGA", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HGD", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Hangzhou", "code": "HGH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Helgoland", "code": "HGL", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON>", "code": "HGN", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Hagerstown Wash County Regional", "code": "HGR", "stateCode": "MD", "countryCode": "US", "countryName": "United States"}, {"name": "Mount Hagen Kagamuga", "code": "HGU", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON>", "code": "HHH", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "Frankfurt Hahn", "code": "HHN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Hua Hin Airport", "code": "HHQ", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HHZ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Hibbing <PERSON><PERSON><PERSON>", "code": "HIB", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Horn Island", "code": "HID", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Hiroshima International", "code": "HIJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Shillavo", "code": "HIL", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "<PERSON><PERSON>", "code": "HIN", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Hon<PERSON>", "code": "HIR", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Hayman Island", "code": "HIS", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "HJR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Healy Lake", "code": "HKB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Hakodate", "code": "HKD", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Hong Kong Intl", "code": "HKG", "stateCode": "", "countryCode": "HK", "countryName": "Hong Kong"}, {"name": "Hokitika Arpt", "code": "HKK", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HKN", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Phuket International", "code": "HKT", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON>", "code": "HLD", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HLH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Helena", "code": "HLN", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Holyhead", "code": "HLY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON>", "code": "HLZ", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Khanty Mansiysk", "code": "HMA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "HME", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Hermosillo Gen P<PERSON>que<PERSON> Garcia", "code": "HMO", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HMV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HNA", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Tokyo Haneda", "code": "HND", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HNH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Honolulu International Airport", "code": "HNL", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "HNM", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "Haines Municipal", "code": "HNS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Hobbs Lea County", "code": "HOB", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HOD", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HOE", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HOF", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HOG", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Hohenems", "code": "HOH", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "Hao Island", "code": "HOI", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON>", "code": "HOM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Huron Howes", "code": "HON", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "HOQ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Horta", "code": "HOR", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "<PERSON>", "code": "HOU", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Orsta Volda Hovden", "code": "HOV", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "HaApai Salote Pilolevu", "code": "HPA", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Hooper Bay", "code": "HPB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HPH", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Westchester County Apt", "code": "HPN", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HRB", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Harare", "code": "HRE", "stateCode": "", "countryCode": "ZW", "countryName": "Zimbabwe"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "HRG", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Kharkov", "code": "HRK", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Harlingen Valley International", "code": "HRL", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Harrogate Linton On Ouse", "code": "HRT", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Saga", "code": "HSG", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Huslia", "code": "HSL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Zhoushan", "code": "HSN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Huntsville Intl", "code": "HSV", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "HTA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Hatanga", "code": "HTG", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Hamilton Island", "code": "HTI", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "HTN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Huntington Ashland Milton Tri State", "code": "HTS", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HUH", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON>", "code": "HUI", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HUN", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "<PERSON><PERSON>", "code": "HUQ", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Hughes Municipal", "code": "HUS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Hudiksvall", "code": "HUV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Huatulco", "code": "HUX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Humberside Arpt", "code": "HUY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Huizhou", "code": "HUZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HVA", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Hervey Bay", "code": "HVB", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HVD", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "Honningsvag <PERSON>", "code": "HVG", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "New Haven", "code": "HVN", "stateCode": "CT", "countryCode": "US", "countryName": "United States"}, {"name": "Havre City County", "code": "HVR", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "HYA", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Hyderabad Rajiv Gandhi International Airport", "code": "HYD", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Hydaburg SPB", "code": "HYG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Hollis SPB", "code": "HYL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "HYN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Hays Municipal", "code": "HYS", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Hanzhong", "code": "HZG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Igarka", "code": "IAA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Washington Dulles Intl", "code": "IAD", "stateCode": "DC", "countryCode": "US", "countryName": "United States"}, {"name": "Niagara Falls International", "code": "IAG", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Houston George <PERSON> Intercntl", "code": "IAH", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "In Amenas", "code": "IAM", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "<PERSON><PERSON> Memorial Airport", "code": "IAN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "IAS", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Ibadan", "code": "IBA", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Ibague", "code": "IBE", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Ibiza", "code": "IBZ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Cicia", "code": "ICI", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Seoul Incheon International", "code": "ICN", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Wichita Dwight <PERSON>", "code": "ICT", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Idaho Falls Fanning Field", "code": "IDA", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Indore", "code": "IDR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Zielona Gora Babimost", "code": "IEG", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Kiev Zhulhany", "code": "IEV", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Isafjordur", "code": "IFJ", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IFN", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "IFO", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Laughlin Bullhead Intl", "code": "IFP", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Inagua", "code": "IGA", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "IGG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kingman", "code": "IGM", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Iguazu Cataratas", "code": "IGR", "stateCode": "MI", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Iguassu Falls Cataratas", "code": "IGU", "stateCode": "PR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Iran Shahr", "code": "IHR", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IIL", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Izhevsk", "code": "IJK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Imam <PERSON> International Airport", "code": "IKA", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Nikolski AFS", "code": "IKO", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Tiksi", "code": "IKS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Irkutsk", "code": "IKT", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Greater Wilmington", "code": "ILG", "stateCode": "DE", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ILI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Wilmington New Hanover County", "code": "ILM", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Wilmington Clinton Field", "code": "ILN", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Iloilo Mandurriao", "code": "ILO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Ile Des Pins", "code": "ILP", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "Ilorin", "code": "ILR", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "<PERSON><PERSON>", "code": "ILY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ILZ", "stateCode": "", "countryCode": "SK", "countryName": "Slovakia"}, {"name": "Imphal Municipal", "code": "IMF", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Simikot", "code": "IMK", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Imperatriz", "code": "IMP", "stateCode": "MA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Iron Mountain Ford", "code": "IMT", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "INC", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Indianapolis International", "code": "IND", "stateCode": "IN", "countryCode": "US", "countryName": "United States"}, {"name": "Inhambane", "code": "INH", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "<PERSON><PERSON>", "code": "INI", "stateCode": "", "countryCode": "RS", "countryName": "Serbia"}, {"name": "International Falls Falls Intl", "code": "INL", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Innsbruck Kranebitten", "code": "INN", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "Winston <PERSON>", "code": "INT", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Nauru Island International", "code": "INU", "stateCode": "", "countryCode": "NR", "countryName": "Nauru"}, {"name": "Inverness", "code": "INV", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "In Salah", "code": "INZ", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "IOA", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Isle Of Man Ronaldsway", "code": "IOM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Impfondo", "code": "ION", "stateCode": "", "countryCode": "CG", "countryName": "Congo (brazzaville)"}, {"name": "<PERSON><PERSON>", "code": "IOS", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Ipota", "code": "IPA", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Easter Island Mataveri Intl", "code": "IPC", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Ipoh", "code": "IPH", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Ipiales San Luis", "code": "IPI", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "El Centro Imperial Imperial County", "code": "IPL", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Ipatinga Usiminas", "code": "IPN", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Williamsport Lycoming County", "code": "IPT", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Ipswich", "code": "IPW", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IQM", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Qingyang", "code": "IQN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IQQ", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Iquitos CF Secada", "code": "IQT", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IRA", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Circle City", "code": "IRC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Lockhart River", "code": "IRG", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "La Rioja", "code": "IRJ", "stateCode": "LR", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Kirksville Municipal", "code": "IRK", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "Mount Isa", "code": "ISA", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Islamabad Intl", "code": "ISB", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ISG", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Williston Sloulin Field Intl", "code": "ISN", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "Kinston Stallings Field", "code": "ISO", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Islip Long Island Macarthur", "code": "ISP", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Istanbul Ataturk", "code": "IST", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Sulaymaniyah International Airport", "code": "ISU", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "Ithaca Tompkins County", "code": "ITH", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Osaka Itami", "code": "ITM", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Hilo Big Island Hilo International", "code": "ITO", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "Niue Island Hanan", "code": "IUE", "stateCode": "", "countryCode": "NU", "countryName": "Niue"}, {"name": "Invercargill", "code": "IVC", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Ivalo", "code": "IVL", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IVR", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Ironwood Gogebic County", "code": "IWD", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "IWJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Iwakuni Kintaikyo Airport", "code": "IWK", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Agartala Singerbhil", "code": "IXA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Bagdogra", "code": "IXB", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Chandigarh", "code": "IXC", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Allahabad Bamrauli", "code": "IXD", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Mangalore Bajpe", "code": "IXE", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Belgaum Sambre", "code": "IXG", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "IXI", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Jammu Satwari", "code": "IXJ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Leh", "code": "IXL", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Madurai", "code": "IXM", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Ranchi", "code": "IXR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "IXS", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Aurangabad Chikkalthana", "code": "IXU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Jamshedpur Sonari", "code": "IXW", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Ka<PERSON><PERSON>", "code": "IXY", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Port Blair", "code": "IXZ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Inyokern Kern County", "code": "IYK", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Izmir", "code": "IZM", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON>", "code": "IZO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Jackson Hole Airport", "code": "JAC", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Jaipur Sanganeer", "code": "JAI", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Jalapa", "code": "JAL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Jackson <PERSON>", "code": "JAN", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JAV", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Jacksonville International", "code": "JAX", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Joacaba", "code": "JCB", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Qasigiannguit", "code": "JCH", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Julia Creek", "code": "JCK", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "JDF", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Jodhpur", "code": "JDH", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Juazeiro Do Norte Regional Do Cariri", "code": "JDO", "stateCode": "CE", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Jingdez<PERSON>", "code": "JDZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Jeddah King <PERSON><PERSON>", "code": "JED", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Jefferson City Memorial", "code": "JEF", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JEG", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "<PERSON>h", "code": "JEJ", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "Jersey States", "code": "JER", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "New York John F <PERSON>", "code": "JFK", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JFR", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Jamnagar Govardhanpur", "code": "JGA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Grand Canyon Heliport", "code": "JGC", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Jiayuguan", "code": "JGN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Qeqertarsuaq", "code": "JGO", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "<PERSON><PERSON>u Sultan Ismail <PERSON>", "code": "JHB", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON>", "code": "JHG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "JHM", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "Sisimiut", "code": "JHS", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Jamestown", "code": "JHW", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Djibouti Ambouli", "code": "JIB", "stateCode": "", "countryCode": "DJ", "countryName": "Djibouti"}, {"name": "Jijiga Jigiga", "code": "JIJ", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Ikaria Island Ikaria", "code": "JIK", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Jim<PERSON>", "code": "JIM", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Jiujiang", "code": "JIU", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Jinjiang", "code": "JJN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Qaqortoq Heliport", "code": "JJU", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "<PERSON><PERSON><PERSON> Axamo", "code": "JKG", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "JKH", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Jakarta", "code": "JKT", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Landskrona Heliport", "code": "JLD", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JLN", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "Jabalpur", "code": "JLR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JMK", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Jamestown", "code": "JMS", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JMU", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Johannesburg Intl", "code": "JNB", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JNN", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Narsaq Heliport", "code": "JNS", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Juneau International", "code": "JNU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Naxos Airport", "code": "JNX", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Jinzhou", "code": "JNZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JOE", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Yogyakarta Adisutjipto", "code": "JOG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JOI", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "JOL", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "JPA", "stateCode": "PB", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON>", "code": "JPR", "stateCode": "RO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JQA", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "New York Downtown Manhattan H P", "code": "JRB", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JRH", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "JRO", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Jaisalmer", "code": "JSA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Sit<PERSON>", "code": "JSH", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JSI", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Sodertalje Heliport", "code": "JSO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "JSR", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "Johnstown Cambria County", "code": "JST", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Maniitsoq Heliport", "code": "JSU", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Syros Island", "code": "JSY", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JTR", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Astypalaia Island Astypalaia", "code": "JTY", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Jujuy El Cadillal", "code": "JUJ", "stateCode": "PJ", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Juliaca", "code": "JUL", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "<PERSON><PERSON><PERSON>", "code": "JUM", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Upernavik Heliport", "code": "JUV", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Juzhou", "code": "JUZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "JVA", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Jyvaskyla", "code": "JYV", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Kariba", "code": "KAB", "stateCode": "", "countryCode": "ZW", "countryName": "Zimbabwe"}, {"name": "Ka<PERSON><PERSON>", "code": "KAD", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Kake SPB", "code": "KAE", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KAJ", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Kaltag", "code": "KAL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Aminu Kano Intl Apt", "code": "KAN", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KAO", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KAT", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KAW", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KAX", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Birch Creek", "code": "KBC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kabul Khwaja Rawash", "code": "KBL", "stateCode": "", "countryCode": "AF", "countryName": "Afghanistan"}, {"name": "Kiev Borispol", "code": "KBP", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Kota Bharu <PERSON>g<PERSON> Chepa", "code": "KBR", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KBV", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON>", "code": "KCA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Coffman Cove SPB", "code": "KCC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KCF", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Chignik Fisheries", "code": "KCG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KCH", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Chignik <PERSON>", "code": "KCL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "KCM", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Chi<PERSON><PERSON>", "code": "KCQ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KCZ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kandahar", "code": "KDH", "stateCode": "", "countryCode": "AF", "countryName": "Afghanistan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KDI", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KDL", "stateCode": "", "countryCode": "EE", "countryName": "Estonia"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "KDM", "stateCode": "", "countryCode": "MV", "countryName": "Maldives"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KDO", "stateCode": "", "countryCode": "MV", "countryName": "Maldives"}, {"name": "Skardu", "code": "KDU", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Kandavu", "code": "KDV", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KEB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Reykjavik Keflavik International", "code": "KEF", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Kemerovo", "code": "KEJ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Ekwok", "code": "KEK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KEM", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Nepalganj", "code": "KEP", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Kerman", "code": "KER", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "KET", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KEW", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KFA", "stateCode": "", "countryCode": "MR", "countryName": "Mauritania"}, {"name": "False Pass", "code": "KFP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KGA", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Kingscote", "code": "KGC", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Kaliningrad", "code": "KGD", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Kagau", "code": "KGE", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Karaganda", "code": "KGF", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Kalgoorlie", "code": "KGI", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "New Koliganek", "code": "KGK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KGL", "stateCode": "", "countryCode": "RW", "countryName": "Rwanda"}, {"name": "Kogalym International", "code": "KGP", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "KGS", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON>", "code": "KGX", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kherson", "code": "KHE", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "<PERSON><PERSON>", "code": "KHG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Kaohsiung International", "code": "KHH", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Karachi Quaid E Azam Intl", "code": "KHI", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KHM", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Nanchang", "code": "KHN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KHS", "stateCode": "", "countryCode": "OM", "countryName": "Oman"}, {"name": "Khabarovsk Novyy", "code": "KHV", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KHZ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KID", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kingfisher Lake", "code": "KIF", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kish Island", "code": "KIH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Niigata", "code": "KIJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "KIK", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "Kimberley", "code": "KIM", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Kingston Norman <PERSON>", "code": "KIN", "stateCode": "", "countryCode": "JM", "countryName": "Jamaica"}, {"name": "Kerry County Airport", "code": "KIR", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KIS", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KIT", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KIV", "stateCode": "", "countryCode": "MD", "countryName": "Moldova"}, {"name": "Osaka Kansai International", "code": "KIX", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Krasnojarsk", "code": "KJA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Koyuk", "code": "KKA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kitoi Bay SPB", "code": "KKB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KKC", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Kokoda", "code": "KKD", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KKE", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KKH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Akiachak Spb", "code": "KKI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kita Kyushu Kokura", "code": "KKJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kirkenes Hoeybuktmoen", "code": "KKN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Kaukura Atoll", "code": "KKR", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Ekuk", "code": "KKU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kalskag Municipal", "code": "KLG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kolhapur", "code": "KLH", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Levelock", "code": "KLL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Larsen Bay Larsen SPB", "code": "KLN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kalibo", "code": "KLO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Kalmar", "code": "KLR", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Klagenfurt", "code": "KLU", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "Karlovy Vary", "code": "KLV", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "Klawock", "code": "KLW", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KLX", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KMA", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "King <PERSON> City King Khalid Military", "code": "KMC", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KME", "stateCode": "", "countryCode": "RW", "countryName": "Rwanda"}, {"name": "Ku<PERSON><PERSON>", "code": "KMG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KMI", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "KMJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Manokotak SPB", "code": "KMO", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KMQ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KMS", "stateCode": "", "countryCode": "GH", "countryName": "Ghana"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KMV", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Moser Bay", "code": "KMY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KND", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Kings Lynn Marham RAF", "code": "KNF", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "KNG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KNH", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Kakhonak", "code": "KNK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "KNQ", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "King Island", "code": "KNS", "stateCode": "TS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Kanpur", "code": "KNU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "New Stuyahok", "code": "KNW", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KNX", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Kona Big Island Keahole", "code": "KOA", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KOC", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KOE", "stateCode": "", "countryCode": "TL", "countryName": "East Timor"}, {"name": "Kirkwall Kirkwall Arpt Orkney Island", "code": "KOI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Kagoshima", "code": "KOJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kokkola Pietarsaari Kruunupyy", "code": "KOK", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Nakhon Phanom", "code": "KOP", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Kotlik", "code": "KOT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "KOU", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Ganzhou", "code": "KOW", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Olga Bay SPB", "code": "KOY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ouzinkie SPB", "code": "KOZ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Point Baker SPB", "code": "KPB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Port Clarence", "code": "KPC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kipnuk SPB", "code": "KPN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pohang", "code": "KPO", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Port Williams SPB", "code": "KPR", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Perryville SPB", "code": "KPV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Port Bailey SPB", "code": "KPY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Akutan", "code": "KQA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ka<PERSON><PERSON>", "code": "KRB", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KRF", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kikori", "code": "KRI", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Krakow J Paul II Balice Intl", "code": "KRK", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KRL", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KRN", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "KRP", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Krasnodar", "code": "KRR", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Kristiansand K<PERSON>", "code": "KRS", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KRY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Kosrae", "code": "KSA", "stateCode": "", "countryCode": "FM", "countryName": "Micronesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KSC", "stateCode": "", "countryCode": "SK", "countryName": "Slovakia"}, {"name": "Karlstad", "code": "KSD", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "KSF", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Kermanshah", "code": "KSH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Kasos Island", "code": "KSJ", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Saint Marys", "code": "KSM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kostanay", "code": "KSN", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Kastoria Aristoteles Airport", "code": "KSO", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KSQ", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Kristiansund Kvernberget", "code": "KSU", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON>", "code": "KSY", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KSZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KTA", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Thorne Bay", "code": "KTB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KTE", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KTM", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Ketchikan International", "code": "KTN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Teller Mission Brevig Mission", "code": "KTS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KTT", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Katowice Pyrzowice", "code": "KTW", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUA", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUD", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Samara", "code": "KUF", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Kubin Island", "code": "KUG", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUH", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kasigluk", "code": "KUK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kuala Lumpur Intl", "code": "KUL", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Kaunas", "code": "KUN", "stateCode": "", "countryCode": "LT", "countryName": "Lithuania"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUO", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUS", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KUT", "stateCode": "", "countryCode": "GE", "countryName": "Georgia"}, {"name": "<PERSON><PERSON> Bhuntar", "code": "KUU", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "KUV", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Kavala Megas Alexandros Apt", "code": "KVA", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Skovde", "code": "KVB", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "King Cove", "code": "KVC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Gyandzha", "code": "KVD", "stateCode": "", "countryCode": "AZ", "countryName": "Azerbaijan"}, {"name": "Kavieng", "code": "KVG", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Kirovsk", "code": "KVK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Kivalina", "code": "KVL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KWA", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "Guiyang", "code": "KWE", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Waterfall SPB", "code": "KWF", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KWG", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Kuwait International", "code": "KWI", "stateCode": "", "countryCode": "KW", "countryName": "Kuwait"}, {"name": "Gwangju", "code": "KWJ", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Kwigillingok", "code": "KWK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KWL", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "KWM", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Quinhagak Kwinhagak", "code": "KWN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "West Point Village SPB", "code": "KWP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kwethluk", "code": "KWT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "KWZ", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Kasaan SPB", "code": "KXA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Koro Island", "code": "KXF", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Komsomolsk Na Amure", "code": "KXK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "KXU", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Konya", "code": "KYA", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "KYK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Milton Keynes", "code": "KYN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Kyaukpyu", "code": "KYP", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON>", "code": "KYS", "stateCode": "", "countryCode": "ML", "countryName": "Mali"}, {"name": "Koyukuk", "code": "KYU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kyzyl", "code": "KYZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Zachar Bay SPB", "code": "KZB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kozani Philippos Airport", "code": "KZI", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Kazan", "code": "KZN", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Kzyl Orda", "code": "KZO", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Kastelorizo", "code": "KZS", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Luanda 4 de Fevereiro", "code": "LAD", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "<PERSON><PERSON>", "code": "LAE", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LAI", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Lages", "code": "LAJ", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Aklavik", "code": "LAK", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lansing Capital City", "code": "LAN", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Laoag", "code": "LAO", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "La Paz Leon", "code": "LAP", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Beida La Braq", "code": "LAQ", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Laramie General Brees Field", "code": "LAR", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Las Vegas McCarran Intl", "code": "LAS", "stateCode": "NV", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LAU", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "Lawton Municipal", "code": "LAW", "stateCode": "OK", "countryCode": "US", "countryName": "United States"}, {"name": "Los Angeles International", "code": "LAX", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Bradford Leeds", "code": "LBA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Lubbock Preston Smith International Airport", "code": "LBB", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Hamburg Luebeck", "code": "LBC", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "K<PERSON><PERSON>z<PERSON>", "code": "LBD", "stateCode": "", "countryCode": "TJ", "countryName": "Tajikistan"}, {"name": "Latrobe Westmoreland County", "code": "LBE", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "North Platte Lee Bird Field", "code": "LBF", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "Paris Le Bourget", "code": "LBG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Liberal Municipal", "code": "LBL", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Long Banga Airfield", "code": "LBP", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Labasa", "code": "LBS", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON>", "code": "LBU", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Libreville", "code": "LBV", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Larnaca", "code": "LCA", "stateCode": "", "countryCode": "CY", "countryName": "Cyprus"}, {"name": "La Ceiba Goloson International", "code": "LCE", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "La Coruna", "code": "LCG", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Lake Charles Municipal", "code": "LCH", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LCJ", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "<PERSON> Rickenbacker", "code": "LCK", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "La Chorrera", "code": "LCR", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "London City Airport", "code": "LCY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LDB", "stateCode": "PR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON> Tarbes Tarbes O<PERSON>", "code": "LDE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Lord Howe Island", "code": "LDH", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Lamidanda", "code": "LDN", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON>", "code": "LDU", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Landivisiau", "code": "LDV", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Londonderry Eglinton", "code": "LDY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Learmonth", "code": "LEA", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Lebanon Hanover Regional White River", "code": "LEB", "stateCode": "NH", "countryCode": "US", "countryName": "United States"}, {"name": "St Petersburg Pulkovo", "code": "LED", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Le Havre Octeville", "code": "LEH", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Almeria", "code": "LEI", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Halle Leipzig", "code": "LEJ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON>", "code": "LEN", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Leinster", "code": "LER", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Leticia Gen AV Cobo", "code": "LET", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Bureta Levuka Airfield", "code": "LEV", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Lexington Blue Grass", "code": "LEX", "stateCode": "KY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LFM", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Lafayette Regional", "code": "LFT", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LFW", "stateCode": "", "countryCode": "TG", "countryName": "Togo"}, {"name": "New York LaGuardia", "code": "LGA", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Long Beach Municipal", "code": "LGB", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LGG", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "Long Island Deadmans Cay", "code": "LGI", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Lang<PERSON><PERSON>", "code": "LGK", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON>", "code": "LGL", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Legaspi", "code": "LGP", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Lago Agrio", "code": "LGQ", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "London Gatwick", "code": "LGW", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Lahore", "code": "LHE", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Lightning Ridge", "code": "LHG", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "London Heathrow", "code": "LHR", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Lanzhou Arpt", "code": "LHW", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Lifou", "code": "LIF", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LIG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Kauai Island Lihue", "code": "LIH", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "Lima Jorge <PERSON>", "code": "LIM", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Milan Linate", "code": "LIN", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Limon", "code": "LIO", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "<PERSON> International Airport", "code": "LIR", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Lisbon Lisboa", "code": "LIS", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Little Rock National Airport", "code": "LIT", "stateCode": "AR", "countryCode": "US", "countryName": "United States"}, {"name": "Loika<PERSON>", "code": "LIW", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Lijiang City Lijiang", "code": "LJG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Ljubljana Brnik", "code": "LJU", "stateCode": "", "countryCode": "SI", "countryName": "Slovenia"}, {"name": "Lakeba", "code": "LKB", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Seattle Lake Union SPB", "code": "LKE", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LKG", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON>", "code": "LKH", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>ak", "code": "LKL", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Leknes", "code": "LKN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Lucknow Amausi", "code": "LKO", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "LLA", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "LingLing", "code": "LLF", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LLI", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Alluitsup <PERSON>", "code": "LLU", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Lilongwe International", "code": "LLW", "stateCode": "", "countryCode": "MW", "countryName": "Malawi"}, {"name": "Lake Minchumina", "code": "LMA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Lama<PERSON><PERSON>", "code": "LMC", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Los Mochis Federal", "code": "LMM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Limbang", "code": "LMN", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Lampedusa", "code": "LMP", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Klamath Falls Kingsley Field", "code": "LMT", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Lake Murray", "code": "LMY", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Lamen Bay", "code": "LNB", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LNE", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Lincang", "code": "LNJ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Lincoln Municipal", "code": "LNK", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LNO", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Lancaster", "code": "LNS", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Lihir Island", "code": "LNV", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Lanai City", "code": "LNY", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LNZ", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "<PERSON><PERSON>", "code": "LOD", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON>", "code": "LOH", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "London", "code": "LON", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Lagos Murtala <PERSON>", "code": "LOS", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Louisville Bowman Field", "code": "LOU", "stateCode": "KY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "LOV", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Las Palmas Arpt De Gran Canaria", "code": "LPA", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "La Paz El Alto", "code": "LPB", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "La Pedrera", "code": "LPD", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Linkoping", "code": "LPI", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Lipetsk", "code": "LPK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Liverpool <PERSON>", "code": "LPL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "LPM", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "LPP", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Luang Prabang", "code": "LPQ", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Lopez Island", "code": "LPS", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Lampang", "code": "LPT", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Lie<PERSON><PERSON>", "code": "LPX", "stateCode": "", "countryCode": "LV", "countryName": "Latvia"}, {"name": "<PERSON>", "code": "LPY", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Puerto Leguizamo", "code": "LQM", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Laredo International", "code": "LRD", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Longreach", "code": "LRE", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "La Rochelle <PERSON>", "code": "LRH", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "La Romana", "code": "LRM", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "Lar A P", "code": "LRR", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "LRS", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON>", "code": "LRT", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Losuia", "code": "LSA", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "La Serena La Florida", "code": "LSC", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "La Crosse Municipal", "code": "LSE", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LSH", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Shetland Islands Sumburgh", "code": "LSI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Las Piedras Josef<PERSON>", "code": "LSP", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "<PERSON><PERSON> de <PERSON>", "code": "LSS", "stateCode": "", "countryCode": "GP", "countryName": "Guadeloupe"}, {"name": "Launceston", "code": "LST", "stateCode": "TS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Lismore", "code": "LSY", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "LTD", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Altai", "code": "LTI", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "London Lunton Luton Airport", "code": "LTN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Loreto", "code": "LTO", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Saint Tropez <PERSON> Mole", "code": "LTT", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Latacunga Cotapaxi International", "code": "LTX", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Lukla", "code": "LUA", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LUD", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "Phoenix Luke AFB", "code": "LUF", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Lugano", "code": "LUG", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "<PERSON> Noble Field", "code": "LUL", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LUM", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Lusaka", "code": "LUN", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "<PERSON><PERSON>", "code": "LUO", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Molokai <PERSON>", "code": "LUP", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "San Luis", "code": "LUQ", "stateCode": "SL", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Cape Lisburne", "code": "LUR", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LUV", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Luxembourg", "code": "LUX", "stateCode": "", "countryCode": "LU", "countryName": "Luxembourg"}, {"name": "Livingstone", "code": "LVI", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "La<PERSON><PERSON>", "code": "LVO", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Lewisburg Greenbrier Valley", "code": "LWB", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "LWN", "stateCode": "", "countryCode": "AM", "countryName": "Armenia"}, {"name": "Lviv Snilow", "code": "LWO", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Lewiston Nez Perce County Rgnl", "code": "LWS", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Lewistown Municipal", "code": "LWT", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LWY", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Lhasa", "code": "LXA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "LXG", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LXR", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Limnos", "code": "LXS", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LYA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Little Cayman", "code": "LYB", "stateCode": "", "countryCode": "KY", "countryName": "Cayman Islands"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "LYC", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Lianyungang", "code": "LYG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Lynchburg Preston Glenn Field", "code": "LYH", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "LYI", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Faisalabad", "code": "LYP", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Longyearbyen Svalbard", "code": "LYR", "stateCode": "", "countryCode": "SJ", "countryName": "Svalbard And <PERSON>"}, {"name": "<PERSON>", "code": "LYS", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON><PERSON>", "code": "LZC", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Liuzhou", "code": "LZH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "LZN", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Luzhou", "code": "LZO", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Chennai", "code": "MAA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Maraba", "code": "MAB", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Madrid Barajas", "code": "MAD", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Midland Odessa Regional", "code": "MAF", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Madang", "code": "MAG", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Menorca", "code": "MAH", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON>", "code": "MAJ", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "Matamoros", "code": "MAM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Manchester International", "code": "MAN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "MAO", "stateCode": "AM", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Maracaibo La Chinita", "code": "MAR", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Manus Island Momote", "code": "MAS", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MAU", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON> Eugen<PERSON>", "code": "MAZ", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Mombasa Moi International", "code": "MBA", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "Mmabatho International", "code": "MBD", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "<PERSON><PERSON><PERSON>", "code": "<PERSON>", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Maryborough", "code": "MBH", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Montego Bay Sangster Intl", "code": "MBJ", "stateCode": "", "countryCode": "JM", "countryName": "Jamaica"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MBL", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Saginaw Bay City Midland Tri City", "code": "MBS", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Masbate", "code": "MBT", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Mbambanakira", "code": "MBU", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Merced Municipal Arpt", "code": "MCE", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MCG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Kansas City International", "code": "MCI", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MCK", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "Monte Carlo Heliport", "code": "MCM", "stateCode": "", "countryCode": "MC", "countryName": "Monaco"}, {"name": "<PERSON>", "code": "MCN", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Orlando International", "code": "MCO", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Macapa Internacional", "code": "MCP", "stateCode": "AP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MCT", "stateCode": "", "countryCode": "OM", "countryName": "Oman"}, {"name": "Mcarthur River", "code": "MCV", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Mason City", "code": "MCW", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Ma<PERSON><PERSON><PERSON><PERSON>", "code": "MCX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Sunshine Coast Maroochydore", "code": "MCY", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Maceio <PERSON>", "code": "MCZ", "stateCode": "AL", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "MDC", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Medellin <PERSON>", "code": "MDE", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Mudanjiang", "code": "MDG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MDK", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "Mandalay Annisaton", "code": "MDL", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Mar Del Plata", "code": "MDQ", "stateCode": "BA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Middle Caicos", "code": "MDS", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "Harrisburg Intl", "code": "MDT", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "MDU", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Chicago Midway", "code": "MDW", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Mendoza El Plumerillo", "code": "MDZ", "stateCode": "MD", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Manta", "code": "MEC", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MED", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Mare", "code": "MEE", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MEG", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MEH", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Meridian Key Field", "code": "MEI", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "Melbourne Tullamarine", "code": "MEL", "stateCode": "VI", "countryCode": "AU", "countryName": "Australia"}, {"name": "Memphis International", "code": "MEM", "stateCode": "TN", "countryCode": "US", "countryName": "United States"}, {"name": "Medan <PERSON>", "code": "MES", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Mexico City Benito Juarez International", "code": "MEX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MEY", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Miller International McAllen", "code": "MFE", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Mo<PERSON>", "code": "MFJ", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON>", "code": "MFK", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Macau", "code": "MFM", "stateCode": "", "countryCode": "MO", "countryName": "Macau"}, {"name": "Medford Jackson County", "code": "MFR", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MFU", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "Managua Augusto <PERSON>", "code": "MGA", "stateCode": "", "countryCode": "NI", "countryName": "Nicaragua"}, {"name": "Mount Gambier", "code": "MGB", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Regional De Maringa", "code": "MGF", "stateCode": "PR", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Margate", "code": "MGH", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Montgomery Dannelly Fld", "code": "MGM", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "Mogadishu International", "code": "MGQ", "stateCode": "", "countryCode": "SO", "countryName": "Somalia"}, {"name": "Mangaia Island", "code": "MGS", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MGT", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Morgantown", "code": "MGW", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MGZ", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MHD", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Mannheim Arpt", "code": "MHG", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Marsh Harbour International", "code": "MHH", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Manhattan Regional Airport", "code": "MHK", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "Minsk International 1", "code": "MHP", "stateCode": "", "countryCode": "BY", "countryName": "Belarus"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MHQ", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Sacramento Mather AFB", "code": "MHR", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Manchester Municipal", "code": "MHT", "stateCode": "NH", "countryCode": "US", "countryName": "United States"}, {"name": "Miami International", "code": "MIA", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "MID", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "MIG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Marilia Dr Gastao Vidigal", "code": "MII", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Milan", "code": "MIL", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Merimbula", "code": "MIM", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Monastir <PERSON>urguiba Intl", "code": "MIR", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "Misima Island", "code": "MIS", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON>", "code": "MJA", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "MJD", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MJF", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MJI", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Monkey Mia Shark Bay", "code": "MJK", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MJL", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MJM", "stateCode": "", "countryCode": "CD", "countryName": "Congo (kinshasa)"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MJN", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Mytilene", "code": "MJT", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON>rcia <PERSON>", "code": "MJV", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Mirnyj", "code": "MJZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Milwaukee General <PERSON>", "code": "MKE", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Muskegon", "code": "MKG", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Hoolehua <PERSON>", "code": "MKK", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MKM", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON>", "code": "MKP", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MKQ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MKR", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Ma<PERSON><PERSON><PERSON>", "code": "MKU", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MKW", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON>", "code": "MKY", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Malta Luqa", "code": "MLA", "stateCode": "", "countryCode": "MT", "countryName": "Malta"}, {"name": "Melbourne Intl", "code": "MLB", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Male International", "code": "MLE", "stateCode": "", "countryCode": "MV", "countryName": "Maldives"}, {"name": "Malang", "code": "MLG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Basel Mulhouse EuroAirport French", "code": "MLH", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Moline Quad City", "code": "MLI", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "MLL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Morelia", "code": "MLM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Melilla", "code": "MLN", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON>", "code": "MLO", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Monroe Municipal", "code": "MLU", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "Malatya", "code": "MLX", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Manley Hot Springs", "code": "MLY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Malmo", "code": "MMA", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MMB", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Durham Tees Valley", "code": "MME", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Mount Magnet", "code": "MMG", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Mammoth Lakes", "code": "MMH", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Murmansk", "code": "MMK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Vila <PERSON>", "code": "MMO", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "Mal<PERSON> Sturup", "code": "MMX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MMY", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Mana Island Airstrip", "code": "MNF", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MNG", "stateCode": "NT", "countryCode": "AU", "countryName": "Australia"}, {"name": "Manan<PERSON><PERSON>", "code": "MNJ", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Manila Ninoy Aquino Intl", "code": "MNL", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Minto", "code": "MNT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MNU", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Moa Orestes Acosta", "code": "MOA", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Mobile Municipal Arpt", "code": "MOB", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "Montes Claros", "code": "MOC", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Modesto Municipal", "code": "MOD", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MOF", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Mong Hsat", "code": "MOG", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Mitiaro Island", "code": "MOI", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "<PERSON><PERSON> A<PERSON>", "code": "MOL", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "MOQ", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Minot International", "code": "MOT", "stateCode": "ND", "countryCode": "US", "countryName": "United States"}, {"name": "Mountain Village", "code": "MOU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Moranbah", "code": "MOV", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Moscow", "code": "M<PERSON>", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "MOZ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MPA", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "Caticlan Malay", "code": "MPH", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Montpellier Meditarranee", "code": "MPL", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Maputo International", "code": "MPM", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Mount Pleasant", "code": "MPN", "stateCode": "", "countryCode": "FK", "countryName": "Falkland Islands"}, {"name": "Mariupol", "code": "MPW", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Magnitogorsk", "code": "MQF", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MQL", "stateCode": "VI", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "MQM", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON>", "code": "MQN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Nelspruit Kruger <PERSON>", "code": "MQP", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Marquette Sawyer International", "code": "MQT", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Makale", "code": "MQX", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MRA", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Merida A Carnevalli", "code": "MRD", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Mara Lodges", "code": "MRE", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "Marseille", "code": "MRS", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Mauritius SSeewoosa<PERSON><PERSON>", "code": "MRU", "stateCode": "", "countryCode": "MU", "countryName": "Mauritius"}, {"name": "Mineralnye Vody", "code": "MRV", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Carmel Monterey Peninsula", "code": "MRY", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "MRZ", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Muskrat Dam", "code": "MSA", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Manston Kent International", "code": "MSE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MSJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Florence Muscle Shoals Sheffield", "code": "MSL", "stateCode": "AL", "countryCode": "US", "countryName": "United States"}, {"name": "Madison Dane County Regional", "code": "MSN", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MSO", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Minneapolis St Paul International", "code": "MSP", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Minsk International 2", "code": "MSQ", "stateCode": "", "countryCode": "BY", "countryName": "Belarus"}, {"name": "<PERSON>s", "code": "MSR", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "MSS", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Aachen Maastricht", "code": "MST", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Maseru Moshoeshoe Intl", "code": "MSU", "stateCode": "", "countryCode": "LS", "countryName": "Lesotho"}, {"name": "Massawa", "code": "MSW", "stateCode": "", "countryCode": "ER", "countryName": "Eritrea"}, {"name": "New Orleans Louis <PERSON>", "code": "MSY", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "MSZ", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "<PERSON><PERSON>", "code": "MTJ", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "Metlakatla SPB", "code": "MTM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Monteria S Jeronimo", "code": "MTR", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MTS", "stateCode": "", "countryCode": "SZ", "countryName": "Swaziland"}, {"name": "Minatitlan", "code": "MTT", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "MTV", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Monterrey Gen <PERSON>", "code": "MTY", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "MUA", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON>", "code": "MUB", "stateCode": "", "countryCode": "BW", "countryName": "Botswana"}, {"name": "Munich Franz <PERSON>", "code": "MUC", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Big Island Kamuela", "code": "MUE", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "Mersa Matruh", "code": "MUH", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Mauke Island", "code": "MUK", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MUN", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "<PERSON><PERSON>", "code": "MUR", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Multa<PERSON>", "code": "MUX", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MUZ", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "<PERSON><PERSON>", "code": "MVB", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Montevideo Carrasco", "code": "MVD", "stateCode": "", "countryCode": "UY", "countryName": "Uruguay"}, {"name": "<PERSON><PERSON>", "code": "MVP", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MVR", "stateCode": "", "countryCode": "CM", "countryName": "Cameroon"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MVS", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Mataiva", "code": "MVT", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON>", "code": "MVY", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Marion Williamson County", "code": "MWA", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "MWF", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Mag<PERSON>", "code": "MWQ", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MWZ", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "<PERSON><PERSON>", "code": "MXH", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Mexicali", "code": "MXL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Morombe", "code": "MXM", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Mo<PERSON>aix", "code": "MXN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Milan Malpensa", "code": "MXP", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MXT", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "<PERSON><PERSON>", "code": "MXV", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "<PERSON><PERSON>", "code": "MXX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Meixia<PERSON>", "code": "MXZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MYA", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Malindi", "code": "MYD", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MYE", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Mayaguana", "code": "MYG", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Murray Island", "code": "MYI", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MYJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MYL", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Myrtle Beach AFB", "code": "MYR", "stateCode": "SC", "countryCode": "US", "countryName": "United States"}, {"name": "Myit<PERSON>ina", "code": "MYT", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Mekoryuk Ellis Field", "code": "MYU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Mtwara", "code": "MYW", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "<PERSON><PERSON>", "code": "MYY", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MZG", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Merzifon", "code": "MZH", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MZI", "stateCode": "", "countryCode": "ML", "countryName": "Mali"}, {"name": "<PERSON><PERSON><PERSON>", "code": "MZL", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Manzanillo Sierra Maestra", "code": "MZO", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON><PERSON>", "code": "MZR", "stateCode": "", "countryCode": "AF", "countryName": "Afghanistan"}, {"name": "Mazatlan Gen <PERSON>", "code": "MZT", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "MZV", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NAA", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Naracoorte", "code": "NAC", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Nagpur Sonegaon", "code": "NAG", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NAJ", "stateCode": "", "countryCode": "AZ", "countryName": "Azerbaijan"}, {"name": "Nadi International", "code": "NAN", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NAO", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Naples", "code": "NAP", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NAQ", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Nassau Intl", "code": "NAS", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Sao Gonzalo <PERSON>", "code": "NAT", "stateCode": "RN", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Napuka Island", "code": "NAU", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NAV", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NAW", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Beijing Nanyuan Airport", "code": "NAY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Naberevnye Chelny", "code": "NBC", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NBE", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "Nairobi Jomo Kenyatta Intl", "code": "NBO", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "Guantanamo NAS", "code": "NBW", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NBX", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "North Caicos", "code": "NCA", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "Nice Cote DAzur", "code": "NCE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Newcastle Airport", "code": "NCL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "New Chenega", "code": "NCN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Luzon Is Cubi Pt NAS", "code": "NCP", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NCU", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "<PERSON><PERSON>", "code": "NCY", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Nouadhibou", "code": "NDB", "stateCode": "", "countryCode": "MR", "countryName": "Mauritania"}, {"name": "Qiqihar", "code": "NDG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Ndjamena", "code": "NDJ", "stateCode": "", "countryCode": "TD", "countryName": "Chad"}, {"name": "<PERSON><PERSON>", "code": "NDR", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NER", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NEV", "stateCode": "", "countryCode": "KN", "countryName": "Saint Kitts And Nevis"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NFO", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Ningbo", "code": "NGB", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>und<PERSON>", "code": "NGE", "stateCode": "", "countryCode": "CM", "countryName": "Cameroon"}, {"name": "Ngau Island", "code": "NGI", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Nagoya Chubu Centrair Intl", "code": "NGO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Nagasaki", "code": "NGS", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Nha Trang", "code": "NHA", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON>", "code": "NHV", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON>", "code": "NIB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Niamey", "code": "NIM", "stateCode": "", "countryCode": "NE", "countryName": "Niger"}, {"name": "Jacksonville NAS", "code": "NIP", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Nizhnevartovsk", "code": "NJC", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Nouakchott", "code": "NKC", "stateCode": "", "countryCode": "MR", "countryName": "Mauritania"}, {"name": "Nanjing Nanking", "code": "NKG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NKI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NLA", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "Nuevo Laredo Intl Quetzalcoatl", "code": "NLD", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Darnley Island", "code": "NLF", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON>", "code": "NLG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Norfolk Island", "code": "NLK", "stateCode": "", "countryCode": "NF", "countryName": "Norfolk Island"}, {"name": "<PERSON><PERSON>", "code": "NLV", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Namangan", "code": "NMA", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Nightmute", "code": "NME", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Santa Ana", "code": "NNB", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Nanning", "code": "NNG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Nondalton", "code": "NNL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "NNM", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON>", "code": "NNT", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Nanyang", "code": "NNY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Nosara Beach", "code": "NOB", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Knock International", "code": "NOC", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "Nojabrxsk", "code": "NOJ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Nossi be Fascene", "code": "NOS", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Noumea Tontouta", "code": "NOU", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NOV", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Novokuznetsk", "code": "NOZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Napier Hastings Hawkes Bay", "code": "NPE", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "New Plymouth", "code": "NPL", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Neuquen", "code": "NQN", "stateCode": "NE", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Nottingham Airport", "code": "NQT", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nuqui", "code": "NQU", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Newquay St Mawgan", "code": "NQY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NRA", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Norrkoping Kungsangen", "code": "NRK", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Tokyo Narita", "code": "NRT", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Bimini North Seaplane Base", "code": "NSB", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Now Shahr", "code": "NSH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Yaounde Nsimalen", "code": "NSI", "stateCode": "", "countryCode": "CM", "countryName": "Cameroon"}, {"name": "Norilsk", "code": "NSK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON>", "code": "NSN", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Nakhon Si Thammarat", "code": "NST", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Nantes Atlantique", "code": "NTE", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Nantong", "code": "NTG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Newcastle Williamtown", "code": "NTL", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON>ton", "code": "NTN", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Niuatoputapu <PERSON>", "code": "NTT", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Nuremberg Airport", "code": "NUE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "NUI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Nukutavake", "code": "NUK", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NUL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "NUP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Norsup", "code": "NUS", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Novy Urengoy", "code": "NUX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Neiva La Marguita", "code": "NVA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NVK", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Novgorod", "code": "NVR", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Navegantes", "code": "NVT", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NWA", "stateCode": "", "countryCode": "KM", "countryName": "Comoros"}, {"name": "Norwich Arpt", "code": "NWI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "New York", "code": "NYC", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "NYK", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON><PERSON>", "code": "NYM", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Stockholm Skavsta", "code": "NYO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON> u", "code": "NYU", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Orange Springhill", "code": "OAG", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Jacksonville Albert <PERSON>", "code": "OAJ", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Oakland Metropolitan Oak Intl", "code": "OAK", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OAM", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Oaxaca Xoxocotlan", "code": "OAX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Oban Connel", "code": "OBN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OBO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kobuk Wien", "code": "OBU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "OBX", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Coca", "code": "OCC", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Long Seridan", "code": "ODN", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Odessa Central", "code": "ODS", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Oak Harbor", "code": "ODW", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "ODY", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Ornskoldsvik", "code": "OER", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OFI", "stateCode": "", "countryCode": "CI", "countryName": "Ivory Coast"}, {"name": "Kahului Airport Kahului Maui", "code": "OGG", "stateCode": "HI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OGN", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Ogdensburg", "code": "OGS", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Ouargla Ain <PERSON>", "code": "OGX", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Vladikavkaz", "code": "OGZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "OHD", "stateCode": "", "countryCode": "MK", "countryName": "Macedonia"}, {"name": "Okhotsk", "code": "OHO", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Oshima", "code": "OIM", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "OIT", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Okinawa Naha", "code": "OKA", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Oklahoma City Will Rogers World", "code": "OKC", "stateCode": "OK", "countryCode": "US", "countryName": "United States"}, {"name": "Sapporo Okadama", "code": "OKD", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Oki Island", "code": "OKI", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "OKJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Yorke Island", "code": "OKR", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "OKY", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Orland", "code": "OLA", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OLB", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Wolf Point International", "code": "OLF", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Old Harbor SPB", "code": "OLH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "O<PERSON><PERSON><PERSON>", "code": "OLJ", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Olympic Dam", "code": "OLP", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Omaha Eppley Airfield", "code": "OMA", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "Omboue", "code": "OMB", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Ormoc", "code": "OMC", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OMD", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "Nome", "code": "OME", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OMH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "OMO", "stateCode": "", "countryCode": "BA", "countryName": "Bosnia And Herzegovina"}, {"name": "Oradea", "code": "OMR", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Omsk", "code": "OMS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Ondangwa", "code": "OND", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "Mornington", "code": "ONG", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "ONJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "ONL", "stateCode": "NE", "countryCode": "US", "countryName": "United States"}, {"name": "Ontario International", "code": "ONT", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Toksook Bay", "code": "OOK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Gold Coast", "code": "OOL", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Miami Opa Locka", "code": "OPF", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Porto", "code": "OPO", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Sinop", "code": "OPS", "stateCode": "MT", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Balimo", "code": "OPU", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Orebro Bofors", "code": "ORB", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Chicago OHare International", "code": "ORD", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Norfolk International", "code": "ORF", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "Worcester", "code": "ORH", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Port Lions SPB", "code": "ORI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Cork", "code": "ORK", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "Northampton", "code": "ORM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "ORN", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Noorvik Curtis Memorial", "code": "ORV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Paris Orly", "code": "ORY", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Osaka", "code": "OSA", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Ostersund Froesoe", "code": "OSD", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Osijek", "code": "OSI", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Oskarshamn", "code": "OSK", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Oslo Airport", "code": "OSL", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON>", "code": "OSM", "stateCode": "", "countryCode": "IQ", "countryName": "Iraq"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OSR", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "<PERSON><PERSON>", "code": "OSS", "stateCode": "", "countryCode": "KG", "countryName": "Kyrgyzstan"}, {"name": "Ostend Railway", "code": "OST", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "Orsk", "code": "OSW", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OSY", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OSZ", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "North Bend", "code": "OTH", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Bucharest Otopeni International", "code": "OTP", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Coto 47", "code": "OTR", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OTZ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ouagadougou", "code": "OUA", "stateCode": "", "countryCode": "BF", "countryName": "Burkina Faso"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "OUD", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OUE", "stateCode": "", "countryCode": "CG", "countryName": "Congo (brazzaville)"}, {"name": "Oulu", "code": "OUL", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Zouerate", "code": "OUZ", "stateCode": "", "countryCode": "MR", "countryName": "Mauritania"}, {"name": "Novosibirsk Tolmachevo", "code": "OVB", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Asturias", "code": "OVD", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Owensboro Daviess County", "code": "OWB", "stateCode": "KY", "countryCode": "US", "countryName": "United States"}, {"name": "Bissau <PERSON>", "code": "OXB", "stateCode": "", "countryCode": "GW", "countryName": "Guinea-Bissau"}, {"name": "Oxford Kidlington", "code": "OXF", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OXR", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "OYE", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "<PERSON><PERSON>", "code": "OYG", "stateCode": "", "countryCode": "UG", "countryName": "Uganda"}, {"name": "Ozamis City Labo", "code": "OZC", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Zaporozhye", "code": "OZH", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Ouarzazate", "code": "OZZ", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Paderborn", "code": "PAD", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Paducah Barkley Regional", "code": "PAH", "stateCode": "KY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PAI", "stateCode": "", "countryCode": "KH", "countryName": "Cambodia"}, {"name": "Port Au Prince Mais Gate", "code": "PAP", "stateCode": "", "countryCode": "HT", "countryName": "Haiti"}, {"name": "Paris", "code": "PAR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "PAS", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "<PERSON><PERSON>", "code": "PAT", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Poza Rica Tajin", "code": "PAZ", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Puebla Huejotsingo", "code": "PBC", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Porbandar", "code": "PBD", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Plattsburgh AFB", "code": "PBG", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PBH", "stateCode": "", "countryCode": "BT", "countryName": "Bhutan"}, {"name": "West Palm Beach International", "code": "PBI", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PBJ", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Paramaribo Zanderij <PERSON>tl", "code": "PBM", "stateCode": "", "countryCode": "SR", "countryName": "Suriname"}, {"name": "Paraburdoo", "code": "PBO", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "PBP", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Putao", "code": "PBU", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Painter Creek", "code": "PCE", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pucallpa Capitan Rolden", "code": "PCL", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Puerto Carreno", "code": "PCR", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Puerto Inirida", "code": "PDA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Pedro Bay", "code": "PDB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Padang Tabing", "code": "PDG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Ponta Delgada Nordela", "code": "PDL", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Punta Del Este", "code": "PDP", "stateCode": "", "countryCode": "UY", "countryName": "Uruguay"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PDS", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON>", "code": "PDT", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Portland International", "code": "PDX", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Pelican SPB", "code": "PEC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pardubice", "code": "PED", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "Perm", "code": "PEE", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Perugia Sant Egidio", "code": "PEG", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON>", "code": "PEI", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Beijing Capital", "code": "PEK", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Puerto Maldonado", "code": "PEM", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Penang International", "code": "PEN", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Perth", "code": "PER", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Petrozavodsk", "code": "PES", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Pelotas Federal", "code": "PET", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Puerto Lempira", "code": "PEU", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "Peshawar", "code": "PEW", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Pech<PERSON>", "code": "PEX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "PEZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Passo Fundo", "code": "PFB", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Panama City Bay County", "code": "PFN", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Paphos International", "code": "PFO", "stateCode": "", "countryCode": "CY", "countryName": "Cyprus"}, {"name": "Parsabad", "code": "PFQ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Page", "code": "PGA", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Perpignan Llabanere", "code": "PGF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Pangkalpinang", "code": "PGK", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Port Graham", "code": "PGM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pitt Greenville", "code": "PGV", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "PGX", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Port Harcourt", "code": "PHC", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Port Hedland", "code": "PHE", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Hampton Williamsburg Newport News Williamsb", "code": "PHF", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "Port Harcourt City", "code": "PHG", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Philadelphia International", "code": "PHL", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Point Hope", "code": "PHO", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Phitsanulok", "code": "PHS", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Phalaborwa", "code": "PHW", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Phoenix Sky Harbor Intl", "code": "PHX", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Greater Peoria", "code": "PIA", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Hattiesburg Laurel Reg", "code": "PIB", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "Clearwater St Petersburg International", "code": "PIE", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PIF", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Po<PERSON><PERSON>", "code": "PIH", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Glasgow Prestwick", "code": "PIK", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "PIN", "stateCode": "AM", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Pilot Point Arpt", "code": "PIP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "PIR", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "Poitiers <PERSON>", "code": "PIS", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Pittsburgh Intl Apt", "code": "PIT", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PIU", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Pico Island", "code": "PIX", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Point Lay Dew Station", "code": "PIZ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PJA", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "PJG", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Puerto Jimenez", "code": "PJM", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Napaskiak SPB", "code": "PKA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Parkersburg Marietta Wood County", "code": "PKB", "stateCode": "WV", "countryCode": "US", "countryName": "United States"}, {"name": "Petropavlovsk Kamchats", "code": "PKC", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Parkes", "code": "PKE", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Pangkor Airport", "code": "PKG", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Pak<PERSON><PERSON>", "code": "PKK", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON>", "code": "PKP", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PKR", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Pekanbaru Simpang Tiga", "code": "PKU", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Palangkaraya", "code": "PKY", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON>", "code": "PKZ", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Plattsburgh Clinton County", "code": "PLB", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PLD", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Plymouth", "code": "PLH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Placencia", "code": "PLJ", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PLM", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Pellston Emmet County", "code": "PLN", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Port Lincoln", "code": "PLO", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Klaipeda Palanga Palanga International", "code": "PLQ", "stateCode": "", "countryCode": "LT", "countryName": "Lithuania"}, {"name": "Providenciales International", "code": "PLS", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "Belo Horizonte Pampulha", "code": "PLU", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "PLW", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Semipalatinsk", "code": "PLX", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Port Elizabeth", "code": "PLZ", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "P<PERSON><PERSON>", "code": "PMA", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Puerto Montt Tepual", "code": "PMC", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Palmdale Air Force 42", "code": "PMD", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Portsmouth", "code": "PME", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Milan Parma", "code": "PMF", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Palma  Mallorca Palma Mallorca", "code": "PMI", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Palm Island", "code": "PMK", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Port Moller AFS", "code": "PML", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Palermo Punta Raisi", "code": "PMO", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Palmerston North", "code": "PMR", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Porlamar DelCaribe Gen S Marino", "code": "PMV", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Palmas", "code": "PMW", "stateCode": "TO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Puerto Madryn El Tehuelche", "code": "PMY", "stateCode": "CB", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Palmar Sur", "code": "PMZ", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Pamplona", "code": "PNA", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON>unta Gorda", "code": "PND", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "Phnom Penh Pochentong", "code": "PNH", "stateCode": "", "countryCode": "KH", "countryName": "Cambodia"}, {"name": "Pohn<PERSON>i", "code": "PNI", "stateCode": "", "countryCode": "FM", "countryName": "Micronesia"}, {"name": "Pontianak Supadio", "code": "PNK", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Pantelleria", "code": "PNL", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PNP", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Pune Lohegaon", "code": "PNQ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Pointe Noire", "code": "PNR", "stateCode": "", "countryCode": "CG", "countryName": "Congo (brazzaville)"}, {"name": "Pensacola Regional", "code": "PNS", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Petrolina Internacional", "code": "PNZ", "stateCode": "PE", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Porto Alegre Salgado <PERSON>ho", "code": "POA", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Port Gentil", "code": "POG", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Pemba", "code": "POL", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Port Moresby Jackson Fld", "code": "POM", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Puerto Plata La Union", "code": "POP", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "<PERSON><PERSON>", "code": "POR", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Port Of Spain Trinidad Piarco International", "code": "POS", "stateCode": "", "countryCode": "TT", "countryName": "Trinidad And Tobago"}, {"name": "Poughkeepsie Dutchess County", "code": "POU", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Poznan Lawica", "code": "POZ", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Presidente Prudente A De Barros", "code": "PPB", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Puerto Penasco", "code": "PPE", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Pago Pago International", "code": "PPG", "stateCode": "", "countryCode": "AS", "countryName": "American Samoa"}, {"name": "Petropavlovsk", "code": "PPK", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Phap<PERSON>", "code": "PPL", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PPN", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Proserpine Whitsunday Coast", "code": "PPP", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Puerto Princesa", "code": "PPS", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Papeete Faaa", "code": "PPT", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Port Protection", "code": "PPV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PQC", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Presque Isle Municipal", "code": "PQI", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Palenque International Airport", "code": "PQM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Port Macquarie", "code": "PQQ", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Pilot Station", "code": "PQS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "PRC", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Prague Ruzyne", "code": "PRG", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "Praslin Island", "code": "PRI", "stateCode": "", "countryCode": "SC", "countryName": "Seychelles"}, {"name": "P<PERSON><PERSON>", "code": "PRN", "stateCode": "", "countryCode": "RS", "countryName": "Serbia"}, {"name": "<PERSON><PERSON>", "code": "PSA", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Pasco Tri Cities", "code": "PSC", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Ponce Mercedita", "code": "PSE", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Petersburg Municipal", "code": "PSG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pasto Cano", "code": "PSO", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Palm Springs International Airport", "code": "PSP", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "P<PERSON>car<PERSON>", "code": "PSR", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PSS", "stateCode": "MI", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Puerto Suarez", "code": "PSZ", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Port Alsworth", "code": "PTA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "PTF", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Polokwane", "code": "PTG", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Port Heiden", "code": "PTH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Pointe a Pitre Le Raizet", "code": "PTP", "stateCode": "", "countryCode": "GP", "countryName": "Guadeloupe"}, {"name": "Platinum", "code": "PTU", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Panama City Tocumen International", "code": "PTY", "stateCode": "", "countryCode": "PA", "countryName": "Panama"}, {"name": "Pueblo Memorial", "code": "PUB", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PUF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON>unta Cana", "code": "PUJ", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "PUK", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Punta Arenas Pres Ibanez", "code": "PUQ", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "<PERSON><PERSON>", "code": "PUS", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Puerto Asis", "code": "PUU", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Pullman Moscow Regional", "code": "PUW", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "PUY", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Providencia", "code": "PVA", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Provincetown", "code": "PVC", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Providence Theodore <PERSON>", "code": "PVD", "stateCode": "RI", "countryCode": "US", "countryName": "United States"}, {"name": "Shanghai Pudong international Airport", "code": "PVG", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Porto Velho Belmonte", "code": "PVH", "stateCode": "RO", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Lefkas Preveza Aktion", "code": "PVK", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Puerto Vallarta Gustavo <PERSON>", "code": "PVR", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Pevek", "code": "PWE", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Chicago", "code": "PWK", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Portland Intl Jetport", "code": "PWM", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Pavlodar", "code": "PWQ", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Puerto Escondido", "code": "PXM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Porto Santo", "code": "PXO", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Pleik<PERSON>", "code": "PXU", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Puerto Ayacucho", "code": "PYH", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Polyarnyj", "code": "PYJ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Pietermaritzburg", "code": "PZB", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Penzance", "code": "PZE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Puerto Ordaz", "code": "PZO", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "<PERSON>", "code": "QBC", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Bochum", "code": "QBO", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Ashford Intl Rail Station", "code": "QDH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Dusseldorf Station", "code": "QDU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Freiburg", "code": "QFB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Saarbruecken HBF Railway Service", "code": "QFZ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Kolobrzeg Bus Service", "code": "QJY", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Nantes Railway Service", "code": "QJZ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Cologne Railway Service", "code": "QKL", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Mainz", "code": "QMZ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "QOW", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Berlin", "code": "QPP", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Dover Rail Station", "code": "QQD", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Harwich Rail Station", "code": "QQH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "London Kings Cross Rail Service", "code": "QQK", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Manchester Piccadilly Rail Station", "code": "QQM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Birmingham New Street Rail Service", "code": "QQN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "London Paddington Rail Service", "code": "QQP", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Ramsgate Rail Station", "code": "QQR", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Britrail Rail Zone S London St Pancras", "code": "QQS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "London Euston Rail Service", "code": "QQU", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "London Waterloo", "code": "QQW", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Bath Rail Service", "code": "QQX", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "York Rail Service", "code": "QQY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Rotterdam Central Station", "code": "QRH", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Que<PERSON>ro", "code": "QRO", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON>", "code": "QRW", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Tallinn Pirita Harbour", "code": "QUF", "stateCode": "", "countryCode": "EE", "countryName": "Estonia"}, {"name": "<PERSON><PERSON>", "code": "QUL", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Berlin HBF Railway Service", "code": "QWB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Wuerzburg Railway Station", "code": "QWU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Aix en Provence Railway Service", "code": "QXB", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Angers Rail Station", "code": "QXG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Railway   Germany Railway Service", "code": "QYG", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Gavle Railway Service", "code": "QYU", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Uppsala C Railway Service", "code": "QYX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RAB", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON>", "code": "RAE", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RAH", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Prai<PERSON>", "code": "RAI", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "Rajkot Civil", "code": "RAJ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RAK", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Riverside Municipal", "code": "RAL", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Ribeirao Preto Leite Lopes", "code": "RAO", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Rapid City Regional", "code": "RAP", "stateCode": "SD", "countryCode": "US", "countryName": "United States"}, {"name": "Rarotonga", "code": "RAR", "stateCode": "", "countryCode": "CK", "countryName": "Cook Islands"}, {"name": "Rasht", "code": "RAS", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "RBA", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Brooks Lodge", "code": "RBH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Rurrenabaque", "code": "RBQ", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Rio Branco Pres Medici", "code": "RBR", "stateCode": "AC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "RBV", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON>", "code": "RBY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "RCB", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Roche Harbor", "code": "RCE", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RCH", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON>", "code": "RCL", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Richmond", "code": "RCM", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Red Dog", "code": "RDB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>ding", "code": "RDD", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Redmond Bend Roberts Field", "code": "RDM", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "LTS Pulau Redang", "code": "RDN", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Raleigh Durham", "code": "RDU", "stateCode": "NC", "countryCode": "US", "countryName": "United States"}, {"name": "Red Devil", "code": "RDV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "RDZ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "REA", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Recife Guararapes Intl", "code": "REC", "stateCode": "PE", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON>gio Calabria <PERSON>", "code": "REG", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Reykjavik", "code": "REK", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "REL", "stateCode": "CB", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Orenburg", "code": "REN", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "REP", "stateCode": "", "countryCode": "KH", "countryName": "Cambodia"}, {"name": "Resistencia", "code": "RES", "stateCode": "CH", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Reynosa Gen Lucio Blanco", "code": "REX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Greater Rockford", "code": "RFD", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Raiatea", "code": "RFP", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Rio Grande", "code": "RGA", "stateCode": "TF", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "RGI", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Rio Gallegos Internacional", "code": "RGL", "stateCode": "SC", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON>", "code": "RGN", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Rhinelander Oneida County", "code": "RHI", "stateCode": "WI", "countryCode": "US", "countryName": "United States"}, {"name": "Rhodes Diagoras Airport", "code": "RHO", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Santa Maria Base Aerea", "code": "RIA", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Riberalta <PERSON>", "code": "RIB", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Richmond Intl Byrd Field", "code": "RIC", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "Rio Grande", "code": "RIG", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Rio De Janeiro", "code": "RIO", "stateCode": "RJ", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RIS", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Riverside March AFB", "code": "RIV", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Riverton", "code": "RIW", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Riga", "code": "RIX", "stateCode": "", "countryCode": "LV", "countryName": "Latvia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RIY", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "Raja<PERSON><PERSON><PERSON>", "code": "RJA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Rijeka", "code": "RJK", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "RJN", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Rockland Knox County Regional", "code": "RKD", "stateCode": "ME", "countryCode": "US", "countryName": "United States"}, {"name": "Rock Springs Sweetwater County", "code": "RKS", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "RKT", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "Reykjavik Domestic", "code": "RKV", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Rostock Laage Laage", "code": "RLG", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Roma", "code": "RMA", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "RMF", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RMI", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RMP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Taichung RMQ, Chinese Taipei", "code": "RMQ", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Arona Ulawa Airport", "code": "RNA", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RNB", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "RNL", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Bornholm", "code": "RNN", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Reno Tahoe Intl", "code": "RNO", "stateCode": "NV", "countryCode": "US", "countryName": "United States"}, {"name": "Rongelap Island", "code": "RNP", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "Rennes St Jacques", "code": "RNS", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Roanoke Municipal", "code": "ROA", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ROB", "stateCode": "", "countryCode": "LR", "countryName": "Liberia"}, {"name": "Rochester Monroe County", "code": "ROC", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Roi Et Airport", "code": "ROI", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Rockhampton", "code": "ROK", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Rome", "code": "ROM", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Rondonopolis", "code": "ROO", "stateCode": "MT", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Rota", "code": "ROP", "stateCode": "", "countryCode": "MP", "countryName": "Mariana Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ROR", "stateCode": "", "countryCode": "PW", "countryName": "<PERSON><PERSON>"}, {"name": "<PERSON>", "code": "ROS", "stateCode": "SF", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Rotoru<PERSON>", "code": "ROT", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ROV", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Roswell International Air Center", "code": "ROW", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Raipur", "code": "RPR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RRG", "stateCode": "", "countryCode": "MU", "countryName": "Mauritius"}, {"name": "Roros", "code": "RRS", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Santa Rosa", "code": "RSA", "stateCode": "LP", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Rock Sound S Eleuthera", "code": "RSD", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Russian Mission Russian SPB", "code": "RSH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Rosario SPB", "code": "RSJ", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Rochester International Airport", "code": "RST", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RSU", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Fort Myers Southwest Florida Reg", "code": "RSW", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Rotuma Island", "code": "RTA", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RTB", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "Rotterdam Zestienhoven", "code": "RTM", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "<PERSON><PERSON>", "code": "RTW", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RUA", "stateCode": "", "countryCode": "UG", "countryName": "Uganda"}, {"name": "Riyadh King <PERSON><PERSON><PERSON>", "code": "RUH", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Rukumkot", "code": "RUK", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "RUM", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "St Denis de la Reunion Gillot", "code": "RUN", "stateCode": "", "countryCode": "RE", "countryName": "Reunion"}, {"name": "Rurutu", "code": "RUR", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Rutland", "code": "RUT", "stateCode": "VT", "countryCode": "US", "countryName": "United States"}, {"name": "Farafangana", "code": "RVA", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RVE", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON> Ryumsjo<PERSON>", "code": "RVK", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Rovaniemi", "code": "RVN", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON><PERSON>", "code": "RVV", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Roxas City", "code": "RXS", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "RYK", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Rzeszow Jasionka", "code": "RZE", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Ramsar", "code": "RZR", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "<PERSON><PERSON>", "code": "RZS", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Saba Island J Yrausquin", "code": "SAB", "stateCode": "", "countryCode": "BQ", "countryName": "Bes Islands"}, {"name": "Sacramento Executive", "code": "SAC", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Santa Fe", "code": "SAF", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "Sanaa International", "code": "SAH", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "San Salvador Comalapa International", "code": "SAL", "stateCode": "", "countryCode": "SV", "countryName": "El Salvador"}, {"name": "San Diego Lindbergh Fld SDiego", "code": "SAN", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Sao Paulo", "code": "SAO", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "San Pedro Sula Ramon <PERSON>", "code": "SAP", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "San Andros", "code": "SAQ", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "San Antonio International", "code": "SAT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Hilton Head Savannah", "code": "SAV", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Sabiha <PERSON>", "code": "SAW", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Santa Barbara Airport", "code": "SBA", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "St Barthelemy", "code": "SBH", "stateCode": "", "countryCode": "GP", "countryName": "Guadeloupe"}, {"name": "Santa Ana <PERSON>cum<PERSON>", "code": "SBL", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "South Bend Regional", "code": "SBN", "stateCode": "IN", "countryCode": "US", "countryName": "United States"}, {"name": "San Luis Obispo County Airport", "code": "SBP", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Saibai Island", "code": "SBR", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "SBW", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Salisbury Ocean City Wicomico Regional", "code": "SBY", "stateCode": "MD", "countryCode": "US", "countryName": "United States"}, {"name": "Sibiu", "code": "SBZ", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Deadhorse Prudhoe Bay", "code": "SCC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "State College University Park", "code": "SCE", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Stockton", "code": "SCK", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Santiago Arturo <PERSON>", "code": "SCL", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Scammon Bay SPB", "code": "SCM", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Saarbruecken Ensheim", "code": "SCN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Aktau", "code": "SCO", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Santiago De Compostela", "code": "SCQ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Socotra", "code": "SCT", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "Santiago Antonio <PERSON>", "code": "SCU", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SCV", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "SCW", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "SCX", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "San Cristobal Airport", "code": "SCY", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Santa Cruz Is", "code": "SCZ", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Lubango", "code": "SDD", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Santiago Del Estero", "code": "SDE", "stateCode": "SE", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Louisville Standiford Field", "code": "SDF", "stateCode": "KY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SDG", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Sendai", "code": "SDJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SDK", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Harnosand Sundsvall", "code": "SDL", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "SDN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Sand Point Municipal", "code": "SDP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Santo Domingo Las Americas", "code": "SDQ", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "Santander", "code": "SDR", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON>", "code": "SDT", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Rio De Janeiro Santos Dumont", "code": "SDU", "stateCode": "RJ", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Tel Aviv Yafo Sde Dov", "code": "SDV", "stateCode": "", "countryCode": "IL", "countryName": "Israel"}, {"name": "Sidney <PERSON> Municipal", "code": "SDY", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Shetland Islands", "code": "SDZ", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Seattle Tacoma Intl", "code": "SEA", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "SEB", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "Seoul", "code": "SEL", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Southend Municipal", "code": "SEN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Si<PERSON>", "code": "SEW", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Mahe Island Seychelles Intl", "code": "SEZ", "stateCode": "", "countryCode": "SC", "countryName": "Seychelles"}, {"name": "Sfax El Maou", "code": "SFA", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "San Fernando De Apure Las Flecheras", "code": "SFD", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "San Fernando", "code": "SFE", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "St Martin Esperance", "code": "SFG", "stateCode": "", "countryCode": "GP", "countryName": "Guadeloupe"}, {"name": "Kangerlussuaq", "code": "SFJ", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Sao Filipe", "code": "SFL", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "Santa Fe", "code": "SFN", "stateCode": "SF", "countryCode": "AR", "countryName": "Argentina"}, {"name": "San Francisco International", "code": "SFO", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Skelleftea", "code": "SFT", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Springfield", "code": "SFY", "stateCode": "MA", "countryCode": "US", "countryName": "United States"}, {"name": "Surgut", "code": "SGC", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Sonderborg", "code": "SGD", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Springfield Branson Rg", "code": "SGF", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "Ho Chi Minh City", "code": "SGN", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "St George", "code": "SGO", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Saint George Municipal", "code": "SGU", "stateCode": "UT", "countryCode": "US", "countryName": "United States"}, {"name": "Songea", "code": "SGX", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Skagway Municipal", "code": "SGY", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Shanghai Hongqiao International Airport", "code": "SHA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "SHB", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Indaselassie", "code": "SHC", "stateCode": "", "countryCode": "ET", "countryName": "Ethiopia"}, {"name": "Staunton Shenandoah Valley", "code": "SHD", "stateCode": "VA", "countryCode": "US", "countryName": "United States"}, {"name": "Shenyang", "code": "SHE", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Shungnak", "code": "SHG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SHH", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Sharjah", "code": "SHJ", "stateCode": "", "countryCode": "AE", "countryName": "United Arab Emirates"}, {"name": "Shillong", "code": "SHL", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SHM", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Qinhuangdao", "code": "SHP", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON>", "code": "SHR", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Shreveport Regional", "code": "SHV", "stateCode": "LA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SHW", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SHX", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SHY", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Sinop Arpt", "code": "SIC", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Sal Amilcar <PERSON>tl", "code": "SID", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "Simara", "code": "SIF", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "San Juan Isla Grande", "code": "SIG", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Singapore Changi", "code": "SIN", "stateCode": "", "countryCode": "SG", "countryName": "Singapore"}, {"name": "Simferopol", "code": "SIP", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Sitka", "code": "SIT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "San Jose International", "code": "SJC", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Cabo San Lucas Los Cabos", "code": "SJD", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "San Jose Del Gua", "code": "SJE", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "St John Island", "code": "SJF", "stateCode": "", "countryCode": "VI", "countryName": "Virgin Islands, U.S."}, {"name": "San Jose Mcguire Fld", "code": "SJI", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Sarajevo Butmir", "code": "SJJ", "stateCode": "", "countryCode": "BA", "countryName": "Bosnia And Herzegovina"}, {"name": "Sao Jose Dos Campos", "code": "SJK", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "San Jose Juan Santamaria Intl", "code": "SJO", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Sao Jose Do Rio Preto", "code": "SJP", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "San Angelo Mathis Fld", "code": "SJT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "San Juan Luis <PERSON>", "code": "SJU", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Shijiazhuang Daguocun", "code": "SJW", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SJY", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Sao Jorge Island", "code": "SJZ", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "St Kitts Robert L Bradshaw International", "code": "SKB", "stateCode": "", "countryCode": "KN", "countryName": "Saint Kitts And Nevis"}, {"name": "<PERSON><PERSON>", "code": "SKC", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Samarkand", "code": "SKD", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Skien", "code": "SKE", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Thessaloniki Makedonia Apt", "code": "SKG", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Surkhet", "code": "SKH", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Shaktoolik", "code": "SKK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Stokmarknes Skagen", "code": "SKN", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Sokoto", "code": "SKO", "stateCode": "", "countryCode": "NG", "countryName": "Nigeria"}, {"name": "Skopje", "code": "SKP", "stateCode": "", "countryCode": "MK", "countryName": "Macedonia"}, {"name": "Sialkot", "code": "SKT", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "<PERSON><PERSON>", "code": "SKU", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Suk<PERSON>r", "code": "SKZ", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Salta Gen Belgrano", "code": "SLA", "stateCode": "SA", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Salt Lake City International", "code": "SLC", "stateCode": "UT", "countryCode": "US", "countryName": "United States"}, {"name": "Salem Mcnary Field", "code": "SLE", "stateCode": "OR", "countryCode": "US", "countryName": "United States"}, {"name": "Sola", "code": "SLH", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Sol<PERSON><PERSON>", "code": "SLI", "stateCode": "", "countryCode": "ZM", "countryName": "Zambia"}, {"name": "Saranac Lake Adirondack", "code": "SLK", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SLL", "stateCode": "", "countryCode": "OM", "countryName": "Oman"}, {"name": "Salamanca Matacan", "code": "SLM", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Salina", "code": "SLN", "stateCode": "KS", "countryCode": "US", "countryName": "United States"}, {"name": "San Luis Potosi", "code": "SLP", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Sleetmute", "code": "SLQ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SLV", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Saltillo Plan de Guadalupe", "code": "SLW", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Salt Cay", "code": "SLX", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SLY", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "SLZ", "stateCode": "MA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Santa Maria Vila Do Porto", "code": "SMA", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "Sacramento International", "code": "SMF", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Samos", "code": "SMI", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "St Michael", "code": "SMK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Stella Maris Estate Airstrip", "code": "SML", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Salmon", "code": "SMN", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "SMR", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Sainte Marie", "code": "SMS", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Santa Maria Public", "code": "SMX", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Orange County <PERSON>", "code": "SNA", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Salinas", "code": "SNC", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Sao Nicolau <PERSON>", "code": "SNE", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "<PERSON>", "code": "SNN", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "Sakon <PERSON>", "code": "SNO", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Saint Paul Island", "code": "SNP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "St Nazaire <PERSON>", "code": "SNR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Santa Clara", "code": "SNU", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Thandwe", "code": "SNW", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SOB", "stateCode": "", "countryCode": "HU", "countryName": "Hungary"}, {"name": "Solo City Adi Sumarmo", "code": "SOC", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Sofia", "code": "SOF", "stateCode": "", "countryCode": "BG", "countryName": "Bulgaria"}, {"name": "Sogndal <PERSON>", "code": "SOG", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Sorkjosen", "code": "SOJ", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "San Tome El Tigre", "code": "SOM", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Espiritu <PERSON>", "code": "SON", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Soderhamn", "code": "SOO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "SOQ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Southampton Eastleigh", "code": "SOU", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Seldovia", "code": "SOV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Show Low", "code": "SOW", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "St Thomas Island SPB", "code": "SPB", "stateCode": "", "countryCode": "VI", "countryName": "Virgin Islands, U.S."}, {"name": "Santa Cruz De La Palma La Palma", "code": "SPC", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Springfield Capital", "code": "SPI", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Sapporo", "code": "SPK", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Schiphol Railway Service", "code": "SPL", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Saipan International", "code": "SPN", "stateCode": "", "countryCode": "MP", "countryName": "Mariana Islands"}, {"name": "Menongue", "code": "SPP", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "San Pedro", "code": "SPR", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "Wichita Falls Sheppard AFB", "code": "SPS", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Split", "code": "SPU", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SQO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Santa Rosa", "code": "SRA", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "SRE", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SRG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "San Borja Capitan G Q Guardia", "code": "SRJ", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Stord Airport", "code": "SRP", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SRQ", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Stony River", "code": "SRV", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Ser<PERSON>", "code": "SRX", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "<PERSON><PERSON>", "code": "SRY", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Salvador Airport <PERSON>", "code": "SSA", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "St Croix Island SPB", "code": "SSB", "stateCode": "", "countryCode": "VI", "countryName": "Virgin Islands, U.S."}, {"name": "Malabo Santa Isabel", "code": "SSG", "stateCode": "", "countryCode": "GQ", "countryName": "Equatorial Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SSH", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Brunswick Mckinnon", "code": "SSI", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SSJ", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Sault Ste Marie", "code": "SSM", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "SSR", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "MBanza Congo", "code": "SSY", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Saint Cloud Municipal", "code": "STC", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "Santo <PERSON>", "code": "STD", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "St George Island", "code": "STG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Cibao International Airport", "code": "STI", "stateCode": "", "countryCode": "DO", "countryName": "Dominican Republic"}, {"name": "Lambert St Louis Intl", "code": "STL", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "STM", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "London Stansted", "code": "STN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Stockholm", "code": "STO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Stuttgart Echterdingen", "code": "STR", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Santa Rosa Sonoma County", "code": "STS", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "St Thomas Island Cyril E King", "code": "STT", "stateCode": "", "countryCode": "VI", "countryName": "Virgin Islands, U.S."}, {"name": "Surat", "code": "STV", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Stavropol", "code": "STW", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON>ohlsen International Airport", "code": "STX", "stateCode": "", "countryCode": "VI", "countryName": "Virgin Islands, U.S."}, {"name": "Surabaya Juanda", "code": "SUB", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Lamezia Terme S Eufemia", "code": "SUF", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Surigao", "code": "SUG", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Satu <PERSON>", "code": "SUJ", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "<PERSON><PERSON>", "code": "SUL", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Hailey Sun Valley", "code": "SUN", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "Summer Beaver", "code": "SUR", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "SUV", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Sioux City Sioux Gateway", "code": "SUX", "stateCode": "IA", "countryCode": "US", "countryName": "United States"}, {"name": "Savoonga", "code": "SVA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SVB", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Silver City Grant County", "code": "SVC", "stateCode": "NM", "countryCode": "US", "countryName": "United States"}, {"name": "St Vincent ET Joshua", "code": "SVD", "stateCode": "", "countryCode": "VC", "countryName": "Saint Vincent And The Grenadines"}, {"name": "Stavanger Sola", "code": "SVG", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "San Vicente", "code": "SVI", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "SVJ", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SVL", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Moscow Sheremetyevo", "code": "SVO", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Sevilla", "code": "SVQ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Stevens Village", "code": "SVS", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SVU", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Ekaterinburg", "code": "SVX", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "San Antonio", "code": "SVZ", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SWA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON>", "code": "SWF", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "South West Bay", "code": "SWJ", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Milan Segrate", "code": "SWK", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Stillwater Regional Airport", "code": "SWO", "stateCode": "OK", "countryCode": "US", "countryName": "United States"}, {"name": "Swansea Fairwood Comm", "code": "SWS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Strasbourg Entzheim", "code": "SXB", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Berlin Schoenefeld", "code": "SXF", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON> Collooney", "code": "SXL", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "St Maarten Princ Juliana", "code": "SXM", "stateCode": "", "countryCode": "SX", "countryName": "Sint Maarten"}, {"name": "Sheldon Point Sheldon SPB", "code": "SXP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Srinagar", "code": "SXR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Seal Bay", "code": "SYB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Sydney Kingsford Smith", "code": "SYD", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "SYJ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Simao", "code": "SYM", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "SYO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Syracuse Hancock Intl", "code": "SYR", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Sue Island Warraber Island", "code": "SYU", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Sanya", "code": "SYX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Stornoway", "code": "SYY", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Shiraz", "code": "SYZ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Soyo", "code": "SZA", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Kuala Lumpur Sultan <PERSON>", "code": "SZB", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Sheffield City Airport", "code": "SZD", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "SZF", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Salzburg WA Mozart", "code": "SZG", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "Suzhou", "code": "SZV", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Shenzhen", "code": "SZX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON>z<PERSON><PERSON><PERSON>", "code": "SZZ", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "Tobago", "code": "TAB", "stateCode": "", "countryCode": "TT", "countryName": "Trinidad And Tobago"}, {"name": "Tacloban DZ Romualdez", "code": "TAC", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Daegu", "code": "TAE", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Tagbilaran", "code": "TAG", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "TAH", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Taiz Al Janad", "code": "TAI", "stateCode": "", "countryCode": "YE", "countryName": "Yemen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TAK", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "TAL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Tampico Gen F Javier <PERSON>", "code": "TAM", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Qingdao", "code": "TAO", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Tapachula International", "code": "TAP", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Tashkent Vostochny", "code": "TAS", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "<PERSON><PERSON>", "code": "TAT", "stateCode": "", "countryCode": "SK", "countryName": "Slovakia"}, {"name": "<PERSON><PERSON>", "code": "TBB", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Tabubil", "code": "TBG", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Tablas", "code": "TBH", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "The Bight", "code": "TBI", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Tabarka", "code": "TBJ", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "Fort Leonard Wood Forney AAF", "code": "TBN", "stateCode": "MO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TBO", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Tumbes", "code": "TBP", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Tbilisi Novo Alexeyevka", "code": "TBS", "stateCode": "", "countryCode": "GE", "countryName": "Georgia"}, {"name": "NukuAlofa FuaAmotu Internationa", "code": "TBU", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Tambov", "code": "TBW", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Tabriz", "code": "TBZ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Treasure Cay", "code": "TCB", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TCE", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "Tchibanga", "code": "TCH", "stateCode": "", "countryCode": "GA", "countryName": "Gabon"}, {"name": "Tenerife", "code": "TCI", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Tumaco La Florida", "code": "TCO", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Taba International", "code": "TCP", "stateCode": "", "countryCode": "EG", "countryName": "Egypt"}, {"name": "Tacna", "code": "TCQ", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Tu<PERSON><PERSON><PERSON>", "code": "TCR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Takotna", "code": "TCT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Trinidad", "code": "TDD", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Trat", "code": "TDX", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Teterboro", "code": "TEB", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "Tbessa", "code": "TEE", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Tatitlek", "code": "TEK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "TEN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Terceira Island Lajes", "code": "TER", "stateCode": "", "countryCode": "PT", "countryName": "Portugal"}, {"name": "<PERSON><PERSON>", "code": "TET", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Telluride", "code": "TEX", "stateCode": "CO", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "TFI", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Tenerife Norte Los Rodeos", "code": "TFN", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Tenerife Sur Reina Sofia", "code": "TFS", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Podgorica Golubovci", "code": "TGD", "stateCode": "", "countryCode": "ME", "countryName": "Montenegro"}, {"name": "Kuala Terengganu <PERSON>", "code": "TGG", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Tongoa", "code": "TGH", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Tiga", "code": "TGJ", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TGM", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TGO", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Touggourt", "code": "TGR", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Tegucigalpa Toncontin", "code": "TGU", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "Tuxtla Guti<PERSON>rez <PERSON> San Juan", "code": "TGZ", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "THE", "stateCode": "PI", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Tachilek", "code": "THL", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Trollhattan", "code": "THN", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Thorshofn", "code": "THO", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Sukhothai", "code": "THS", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "THU", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Tirana Rinas", "code": "TIA", "stateCode": "", "countryCode": "AL", "countryName": "Albania"}, {"name": "<PERSON><PERSON>", "code": "TIF", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Tikehau Atoll", "code": "TIH", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Tiju<PERSON> Rodriguez", "code": "TIJ", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Tembagapura Timika", "code": "TIM", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TIN", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Tripoli International", "code": "TIP", "stateCode": "", "countryCode": "LY", "countryName": "Libya"}, {"name": "<PERSON><PERSON>", "code": "TIQ", "stateCode": "", "countryCode": "MP", "countryName": "Mariana Islands"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TIR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Thursday Island", "code": "TIS", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Timaru", "code": "TIU", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Tivat", "code": "TIV", "stateCode": "", "countryCode": "ME", "countryName": "Montenegro"}, {"name": "<PERSON><PERSON>", "code": "TIZ", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TJA", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Tyumen", "code": "TJM", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TJN", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Tanjung Pandan Bulutumbang", "code": "TJQ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Tenakee Springs Tenakee SPB", "code": "TKE", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Bandar Lampung Branti", "code": "TKG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Tok", "code": "TKJ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Truk", "code": "TKK", "stateCode": "", "countryCode": "FM", "countryName": "Micronesia"}, {"name": "Tokunoshima", "code": "TKN", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Takapoto", "code": "TKP", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Kigoma", "code": "TKQ", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Tokushima", "code": "TKS", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Turku", "code": "TKU", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Tatakoto", "code": "TKV", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TKX", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Teller", "code": "TLA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Toluca", "code": "TLC", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TLE", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Tallahassee Municipal", "code": "TLH", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Tatalina AFS", "code": "TLJ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Tallinn Ulemiste", "code": "TLL", "stateCode": "", "countryCode": "EE", "countryName": "Estonia"}, {"name": "Tlemcen Zenata", "code": "TLM", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "<PERSON><PERSON>", "code": "TLN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Toulouse Blagnac", "code": "TLS", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Tuluksak", "code": "TLT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Tel Aviv Yafo Ben Gurion Intl", "code": "TLV", "stateCode": "", "countryCode": "IL", "countryName": "Israel"}, {"name": "Tambolaka", "code": "TMC", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON>", "code": "TME", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TMJ", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Tamale", "code": "TML", "stateCode": "", "countryCode": "GH", "countryName": "Ghana"}, {"name": "Tamatave", "code": "TMM", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Tam<PERSON>e <PERSON>", "code": "TMP", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TMR", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Sao Tome Is", "code": "TMS", "stateCode": "", "countryCode": "ST", "countryName": "Sao Tome And Principe"}, {"name": "Trombetas", "code": "TMT", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Tambor", "code": "TMU", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Tamworth", "code": "TMW", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TMX", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "<PERSON><PERSON>", "code": "TNA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Tin City AFS", "code": "TNC", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "TNG", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TNJ", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Tununak", "code": "TNK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Tainan", "code": "TNN", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TNO", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Antananarivo", "code": "TNR", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Tioman", "code": "TOD", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TOE", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "Tomsk", "code": "TOF", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Togiak Village", "code": "TOG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Torres Airstrip", "code": "TOH", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Toledo Express", "code": "TOL", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Tombouctou", "code": "TOM", "stateCode": "", "countryCode": "ML", "countryName": "Mali"}, {"name": "<PERSON><PERSON>", "code": "TOS", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TOU", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "Toyama", "code": "TOY", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Tampa International", "code": "TPA", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "Taiwan Taoyuan International Airport", "code": "TPE", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Taplejung", "code": "TPJ", "stateCode": "", "countryCode": "NP", "countryName": "Nepal"}, {"name": "Tarapoto", "code": "TPP", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Tepic", "code": "TPQ", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TPS", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "San Domino Island", "code": "TQR", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Torreon", "code": "TRC", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Trondheim Vaernes", "code": "TRD", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Tiree", "code": "TRE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Oslo Sandefjord", "code": "TRF", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Taurang<PERSON>", "code": "TRG", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Bristol Johnson City Kingsport Tri Cities Regional", "code": "TRI", "stateCode": "TN", "countryCode": "US", "countryName": "United States"}, {"name": "Tara<PERSON>", "code": "TRK", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Turin Citta Di Torino", "code": "TRN", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON>", "code": "TRO", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "Trieste Dei Legionari", "code": "TRS", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TRU", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Thiruvananthapuram International", "code": "TRV", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON>", "code": "TRW", "stateCode": "", "countryCode": "KI", "countryName": "Kiribati"}, {"name": "Tiruchirapally Civil", "code": "TRZ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Taipei Sung Shan", "code": "TSA", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "Astana", "code": "TSE", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Venice Treviso", "code": "TSF", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Tsushima", "code": "TSJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON>", "code": "TSL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Tianjin", "code": "TSN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Timisoara", "code": "TSR", "stateCode": "", "countryCode": "RO", "countryName": "Romania"}, {"name": "New York East 34th St Heliport", "code": "TSS", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "TST", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Townsville", "code": "TSV", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON>", "code": "TTA", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TTE", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Tottori", "code": "TTJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Tortuquer<PERSON>", "code": "TTQ", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TTT", "stateCode": "", "countryCode": "TW", "countryName": "Taiwan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TTU", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Tul<PERSON>", "code": "TUA", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Tubuai", "code": "TUB", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TUC", "stateCode": "TU", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Tambacounda", "code": "TUD", "stateCode": "", "countryCode": "SN", "countryName": "Senegal"}, {"name": "Tours St Symphorien", "code": "TUF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Tuguegarao", "code": "TUG", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TUI", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Turbat", "code": "TUK", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Tulsa International", "code": "TUL", "stateCode": "OK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>nis <PERSON>", "code": "TUN", "stateCode": "", "countryCode": "TN", "countryName": "Tunisia"}, {"name": "<PERSON><PERSON>", "code": "TUO", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Tupelo Lemons Municipal", "code": "TUP", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TUR", "stateCode": "PA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Tucson International", "code": "TUS", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Tabuk", "code": "TUU", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Traverse City", "code": "TVC", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Thief River Falls Regional", "code": "TVF", "stateCode": "MN", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TVU", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "<PERSON><PERSON>", "code": "TVY", "stateCode": "", "countryCode": "MM", "countryName": "Myanmar"}, {"name": "Twin Hills", "code": "TWA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Toowoomba", "code": "TWB", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Twin Falls City County", "code": "TWF", "stateCode": "ID", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "TWT", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TWU", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Texarkana Municipal", "code": "TXK", "stateCode": "AR", "countryCode": "US", "countryName": "United States"}, {"name": "Berlin Tegel", "code": "TXL", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TXN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "TYL", "stateCode": "", "countryCode": "PE", "countryName": "Peru"}, {"name": "Taiyuan", "code": "TYN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Tokyo", "code": "TYO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON>", "code": "TYR", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Knoxville M<PERSON>", "code": "TYS", "stateCode": "TN", "countryCode": "US", "countryName": "United States"}, {"name": "Belize City Municipal", "code": "TZA", "stateCode": "", "countryCode": "BZ", "countryName": "Belize"}, {"name": "South Andros", "code": "TZN", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "Trabzon", "code": "TZX", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "<PERSON><PERSON>", "code": "UAH", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Narsarsuaq", "code": "UAK", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Ua Pou", "code": "UAP", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "San Juan", "code": "UAQ", "stateCode": "SJ", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Samburu", "code": "UAS", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "Uberaba", "code": "UBA", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Ube", "code": "UBJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UBP", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Columbus Lowndes County", "code": "UBS", "stateCode": "MS", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UCT", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Uberlandia Ten Cel Aviador Cesar Bombonato", "code": "UDI", "stateCode": "MG", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Uzhgorod", "code": "UDJ", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Udaipur Dabok", "code": "UDR", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Quelimane", "code": "UEL", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Kumejima", "code": "UEO", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Quetta", "code": "UET", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Ufa", "code": "UFA", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Pilot Point Ugashik Bay", "code": "UGB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UGC", "stateCode": "", "countryCode": "UZ", "countryName": "Uzbekistan"}, {"name": "Uganik", "code": "UGI", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "UIB", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON>", "code": "UIH", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UII", "stateCode": "", "countryCode": "HN", "countryName": "Honduras"}, {"name": "Quincy Municipal", "code": "UIN", "stateCode": "IL", "countryCode": "US", "countryName": "United States"}, {"name": "Quito <PERSON>", "code": "UIO", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UIP", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Kobe", "code": "UKB", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Ust Kamenogorsk", "code": "UKK", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ULB", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Ulaanbaatar Buyant Uhaa", "code": "ULN", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "Ulaangom", "code": "ULO", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ULP", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Gulu", "code": "ULU", "stateCode": "", "countryCode": "UG", "countryName": "Uganda"}, {"name": "Ulyanovsk", "code": "ULY", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Uummannaq", "code": "UMD", "stateCode": "", "countryCode": "GL", "countryName": "Greenland"}, {"name": "Umea", "code": "UME", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UNG", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Unalakleet", "code": "UNK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UNN", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Ujung <PERSON>", "code": "UPG", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "Uralsk", "code": "URA", "stateCode": "", "countryCode": "KZ", "countryName": "Kazakhstan"}, {"name": "Urumqi", "code": "URC", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "URE", "stateCode": "", "countryCode": "EE", "countryName": "Estonia"}, {"name": "Uruguaiana R<PERSON>", "code": "URG", "stateCode": "RS", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON>", "code": "URJ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "URO", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Kursk", "code": "URS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Surat Thani", "code": "URT", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "URY", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "Ushuaia Islas <PERSON>", "code": "USH", "stateCode": "TF", "countryCode": "AR", "countryName": "Argentina"}, {"name": "Usinsk", "code": "USK", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON>", "code": "USM", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "USN", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "<PERSON><PERSON>", "code": "USQ", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Busuanga", "code": "USU", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "<PERSON><PERSON>", "code": "UTH", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Upington", "code": "UTN", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "Utapao", "code": "UTP", "stateCode": "", "countryCode": "TH", "countryName": "Thailand"}, {"name": "Umtata", "code": "UTT", "stateCode": "", "countryCode": "ZA", "countryName": "South Africa"}, {"name": "<PERSON><PERSON>", "code": "UUD", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "UUN", "stateCode": "", "countryCode": "MN", "countryName": "Mongolia"}, {"name": "Yuzhno Sakhalinsk", "code": "UUS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Ouvea", "code": "UVE", "stateCode": "", "countryCode": "NC", "countryName": "New Caledonia"}, {"name": "St Lucia Hewanorra", "code": "UVF", "stateCode": "", "countryCode": "LC", "countryName": "Saint Lucia"}, {"name": "Wiesbaden", "code": "UWE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Yulin", "code": "UYN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VAA", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "<PERSON><PERSON>", "code": "VAI", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "<PERSON>eva<PERSON>", "code": "VAK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON>", "code": "VAN", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Suavanao Airstrip", "code": "VAO", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VAR", "stateCode": "", "countryCode": "BG", "countryName": "Bulgaria"}, {"name": "<PERSON><PERSON>", "code": "VAS", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Vavau Lu<PERSON>", "code": "VAV", "stateCode": "", "countryCode": "TO", "countryName": "Tonga"}, {"name": "Vardoe", "code": "VAW", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Verona Montichiari", "code": "VBS", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "VBV", "stateCode": "", "countryCode": "FJ", "countryName": "Fiji"}, {"name": "Visby", "code": "VBY", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Venice Marco Polo", "code": "VCE", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "Campinas", "code": "VCP", "stateCode": "SP", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Victoria County Foster", "code": "VCT", "stateCode": "TX", "countryCode": "US", "countryName": "United States"}, {"name": "Victorville George AFB", "code": "VCV", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VDA", "stateCode": "", "countryCode": "IL", "countryName": "Israel"}, {"name": "Fagernes Valdres", "code": "VDB", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Vitoria Da Conquista", "code": "VDC", "stateCode": "BA", "countryCode": "BR", "countryName": "Brazil"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VDE", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VDM", "stateCode": "RN", "countryCode": "AR", "countryName": "Argentina"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VDS", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Valdez Municipal", "code": "VDZ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VEE", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VEL", "stateCode": "UT", "countryCode": "US", "countryName": "United States"}, {"name": "Veracruz Las Bajadas", "code": "VER", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Vestmannaeyjar", "code": "VEY", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Victoria Falls", "code": "VFA", "stateCode": "", "countryCode": "ZW", "countryName": "Zimbabwe"}, {"name": "Vijayawada", "code": "VGA", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Vigo", "code": "VGO", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Villagarzon", "code": "VGZ", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VHC", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "VHM", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "VHZ", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Vienna International", "code": "VIE", "stateCode": "", "countryCode": "AT", "countryName": "Austria"}, {"name": "El Vigia", "code": "VIG", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Vinh City", "code": "VII", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Virgin Gorda Airport", "code": "VIJ", "stateCode": "", "countryCode": "VG", "countryName": "Virgin Islands, British"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VIL", "stateCode": "", "countryCode": "MA", "countryName": "Morocco"}, {"name": "Visalia", "code": "VIS", "stateCode": "CA", "countryCode": "US", "countryName": "United States"}, {"name": "Vitoria", "code": "VIT", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Vitoria Eurico Sales", "code": "VIX", "stateCode": "ES", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Rach Gia", "code": "VKG", "stateCode": "", "countryCode": "VN", "countryName": "Vietnam"}, {"name": "Moscow Vnukovo", "code": "VKO", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Vorkuta", "code": "VKT", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Valencia", "code": "VLC", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Valdosta Regional", "code": "VLD", "stateCode": "GA", "countryCode": "US", "countryName": "United States"}, {"name": "Port Vila Bauerfield", "code": "VLI", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Valladoli<PERSON>", "code": "VLL", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Valencia", "code": "VLN", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Valesdir", "code": "VLS", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "<PERSON><PERSON>", "code": "VLV", "stateCode": "", "countryCode": "VE", "countryName": "Venezuela"}, {"name": "Vilnius", "code": "VNO", "stateCode": "", "countryCode": "LT", "countryName": "Lithuania"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VNS", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "VNX", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Volgograd", "code": "VOG", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "Volos Nea <PERSON>", "code": "VOL", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Voronezh", "code": "VOZ", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VPE", "stateCode": "", "countryCode": "AO", "countryName": "Angola"}, {"name": "Vopnafjordur", "code": "VPN", "stateCode": "", "countryCode": "IS", "countryName": "Iceland"}, {"name": "Destin Fort Walton Beach", "code": "VPS", "stateCode": "FL", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VPY", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Vieques", "code": "VQS", "stateCode": "", "countryCode": "PR", "countryName": "Puerto Rico"}, {"name": "Varader<PERSON>", "code": "VRA", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Virac", "code": "VRC", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Varkaus", "code": "VRK", "stateCode": "", "countryCode": "FI", "countryName": "Finland"}, {"name": "Verona", "code": "VRN", "stateCode": "", "countryCode": "IT", "countryName": "Italy"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VRY", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Villahermosa Capitan <PERSON>", "code": "VSA", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Lugansk", "code": "VSG", "stateCode": "", "countryCode": "UA", "countryName": "Ukraine"}, {"name": "Stockholm Hasslo Vasteras", "code": "VST", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "VTE", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Las Tunas", "code": "VTU", "stateCode": "", "countryCode": "CU", "countryName": "Cuba"}, {"name": "Vishakhapatnam", "code": "VTZ", "stateCode": "", "countryCode": "IN", "countryName": "India"}, {"name": "Valledupar", "code": "VUP", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Villavicencio La Vanguardia", "code": "VVC", "stateCode": "", "countryCode": "CO", "countryName": "Colombia"}, {"name": "Santa Cruz Viru Viru Intl", "code": "VVI", "stateCode": "", "countryCode": "BO", "countryName": "Bolivia"}, {"name": "Vladivostok", "code": "VVO", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VVZ", "stateCode": "", "countryCode": "DZ", "countryName": "Algeria"}, {"name": "Lichinga", "code": "VXC", "stateCode": "", "countryCode": "MZ", "countryName": "Mozambique"}, {"name": "Sao Vicente San Pedro", "code": "VXE", "stateCode": "", "countryCode": "CV", "countryName": "Cape Verde"}, {"name": "<PERSON><PERSON><PERSON>", "code": "VXO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Wales", "code": "WAA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON>", "code": "WAE", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WAG", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "WAQ", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Washington", "code": "WAS", "stateCode": "DC", "countryCode": "US", "countryName": "United States"}, {"name": "Waterford", "code": "WAT", "stateCode": "", "countryCode": "IE", "countryName": "Ireland"}, {"name": "<PERSON> <PERSON>", "code": "WAW", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "WBB", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Beaver", "code": "WBQ", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Windhoek Hosea Kutako Intl", "code": "WDH", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WEF", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Weihai", "code": "WEH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "WEI", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Wagga Wagga Forrest Hill", "code": "WGA", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "WGE", "stateCode": "NS", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "WGP", "stateCode": "", "countryCode": "ID", "countryName": "Indonesia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "WHK", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Wick", "code": "WIC", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nairobi Wilson", "code": "WIL", "stateCode": "", "countryCode": "KE", "countryName": "Kenya"}, {"name": "<PERSON><PERSON>", "code": "WIN", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "WJA", "stateCode": "", "countryCode": "MH", "countryName": "Marshall Islands"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WJU", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "<PERSON><PERSON>", "code": "WKA", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Wakkanai Hokkaido", "code": "WKJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Aleknagik", "code": "WKK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Wellington International", "code": "WLG", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WLH", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Selawik", "code": "WLK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Wallis Island", "code": "WLS", "stateCode": "", "countryCode": "WF", "countryName": "Wallis And <PERSON>"}, {"name": "<PERSON><PERSON>", "code": "WMK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Maroantsetra", "code": "WMN", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "White Mountain", "code": "WMO", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WMR", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "Napakiak SPB", "code": "WNA", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Wunnummin Lake", "code": "WNN", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Naga", "code": "WNP", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Windorah", "code": "WNR", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Nawabshah", "code": "WNS", "stateCode": "", "countryCode": "PK", "countryName": "Pakistan"}, {"name": "Wenzhou", "code": "WNZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WPM", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Whangarei", "code": "WRE", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Wrangell SPB", "code": "WRG", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Worland", "code": "WRL", "stateCode": "WY", "countryCode": "US", "countryName": "United States"}, {"name": "Wroclaw Strachowice", "code": "WRO", "stateCode": "", "countryCode": "PL", "countryName": "Poland"}, {"name": "South Naknek", "code": "WSN", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Westerly State", "code": "WST", "stateCode": "RI", "countryCode": "US", "countryName": "United States"}, {"name": "Westsound", "code": "WSX", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Westport", "code": "WSZ", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Noatak", "code": "WTK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "WTL", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "WTS", "stateCode": "", "countryCode": "MG", "countryName": "Madagascar"}, {"name": "<PERSON><PERSON>", "code": "WUH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WUN", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Wuyishan", "code": "WUS", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Wuxi", "code": "WUX", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Walvis Bay Rooikop", "code": "WVB", "stateCode": "", "countryCode": "NA", "countryName": "Namibia"}, {"name": "Wewak <PERSON>", "code": "WWK", "stateCode": "", "countryCode": "PG", "countryName": "Papua New Guinea"}, {"name": "Whale Pass", "code": "WWP", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Newtok", "code": "WWT", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WXN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON><PERSON>", "code": "WYA", "stateCode": "SA", "countryCode": "AU", "countryName": "Australia"}, {"name": "West Yellowstone Yellowstone", "code": "WYS", "stateCode": "MT", "countryCode": "US", "countryName": "United States"}, {"name": "Chapeco", "code": "XAP", "stateCode": "SC", "countryCode": "BR", "countryName": "Brazil"}, {"name": "Bearskin Lake", "code": "XBE", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Birjand", "code": "XBJ", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Christmas Island", "code": "XCH", "stateCode": "", "countryCode": "CX", "countryName": "Christmas Island"}, {"name": "Chalons Sur Marne Vatry Airport", "code": "XCR", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Lille Europe Rail Svc", "code": "XDB", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Ottawa Rail Station", "code": "XDS", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Disneyland Paris Railway Service", "code": "XED", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Strasbourg Bus Service", "code": "XER", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Baden Baden Karlsruhe Bus Service", "code": "XET", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Stockholm STO C Railway Service", "code": "XEV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Sodertalje S Railway", "code": "XEZ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Calais Frethun Rail Station", "code": "XFF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Strangnas Railway Service", "code": "XFH", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Eskilstuna Railway Service", "code": "XFJ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "XFN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Malmo Railway Service", "code": "XFP", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Malmo South Railway", "code": "XFR", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Tierp Railway Service", "code": "XFU", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Lund C Railway Service", "code": "XGC", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Grantham Rail Station", "code": "XGM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Kangiqsualujjuaq", "code": "XGR", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Valence Railway Service", "code": "XHK", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Xichang", "code": "XIC", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Xilinhot", "code": "XIL", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Dresden Railway Station", "code": "XIR", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Leipzig Halle Railway Station", "code": "XIT", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Erfurt Railway Station", "code": "XIU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON>", "code": "XIY", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Reims Champagne Railway Station", "code": "XIZ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "<PERSON><PERSON>", "code": "XKH", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Kuala Lumpur KUL Sentral Rail", "code": "XKL", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "XKS", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "XLB", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "St Louis", "code": "XLS", "stateCode": "", "countryCode": "SN", "countryName": "Senegal"}, {"name": "<PERSON><PERSON><PERSON>", "code": "XMH", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Xiamen", "code": "XMN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON><PERSON>", "code": "XMS", "stateCode": "", "countryCode": "EC", "countryName": "Ecuador"}, {"name": "Yam Island", "code": "XMY", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Northwest Arkansas Regional Airport", "code": "XNA", "stateCode": "AR", "countryCode": "US", "countryName": "United States"}, {"name": "Newport Gwent Rail Station", "code": "XNE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Newark Northgate Rail Station", "code": "XNK", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nottingham Railway Service", "code": "XNM", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Xining", "code": "XNN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Northallerton Rail Station", "code": "XNO", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nuneaton Rail Station", "code": "XNV", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Poitiers Rail Station", "code": "XOP", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Penrith Rail Station", "code": "XPF", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Paris Gare du Nord Rail Stn", "code": "XPG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Montpellier Railway Station", "code": "XPJ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Port Klang Ferry", "code": "XPQ", "stateCode": "", "countryCode": "MY", "countryName": "Malaysia"}, {"name": "Preston Rail Station", "code": "XPT", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Berwick Rail Station", "code": "XQG", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Nottingham Railway Station", "code": "XQH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Lancaster Rail Station", "code": "XQL", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON>", "code": "XQP", "stateCode": "", "countryCode": "CR", "countryName": "Costa Rica"}, {"name": "Qualicum", "code": "XQU", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Runcorn Rail Station", "code": "XRC", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Reading Railway Service", "code": "XRE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Marseille Railway Station", "code": "XRF", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Rugby Rail Station", "code": "XRU", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON> La Frontera La Parra", "code": "XRY", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "South Caicos International", "code": "XSC", "stateCode": "", "countryCode": "TC", "countryName": "Turks And Caicos Islands"}, {"name": "Tours Railway Service", "code": "XSH", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "South Indian Lake", "code": "XSI", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Singapore Seletar", "code": "XSP", "stateCode": "", "countryCode": "SG", "countryName": "Singapore"}, {"name": "Salisbury Rail Station", "code": "XSR", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "XTG", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "Thirsk Rail Station", "code": "XTK", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Tadoule Lake", "code": "XTL", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Xuzhou", "code": "XUZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Stockport Rail Station", "code": "XVA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Stafford Rail Station", "code": "XVB", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Crewe Rail Station", "code": "XVC", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Darlington Rail Station", "code": "XVG", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Peterborough Rail Station", "code": "XVH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Stevenage Rail Station", "code": "XVJ", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Durham Rail Station", "code": "XVU", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Wolverhampton Rail Station", "code": "XVW", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Wakefield Westgate Rail Station", "code": "XWD", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Borlange Falun Falun Railway Service", "code": "XWF", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Strasbourg Railway Station", "code": "XWG", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Stoke on Trent Rail Station", "code": "XWH", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Ronneby Karlskrona Rail Svc", "code": "XWK", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Gothenburg Rail", "code": "XWL", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Hallsberg Rail Station", "code": "XWM", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Warrington BQ Rail Station", "code": "XWN", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Hassleholm", "code": "XWP", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON>", "code": "XWQ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Orebro Railway Service", "code": "XWR", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Swindon Rail Station", "code": "XWS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Varberg Rail Station", "code": "XWV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Nassjo Rail Station", "code": "XWX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Nykoping", "code": "XWZ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Alvesta", "code": "XXA", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Degerfors Rail Station", "code": "XXD", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Koping Rail Station", "code": "XXI", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kat<PERSON><PERSON>", "code": "XXK", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Mjolby Rail Station", "code": "XXM", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kil Rail Service", "code": "XXN", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Leksand Rail Station", "code": "XXO", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Arboga Rail Station", "code": "XXT", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "XXU", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kumla Rail Station", "code": "XXV", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Sundsvall Rail Service", "code": "XXZ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Yandina", "code": "XYA", "stateCode": "", "countryCode": "SB", "countryName": "Solomon Islands"}, {"name": "Borlange Falun Borlange Railway Svc", "code": "XYB", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "XYC", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Lyon Part Dieu Rail Sv", "code": "XYD", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Falkoping Rail Station", "code": "XYF", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Angelholm Helsingborg Helsingborg Railway", "code": "XYH", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "<PERSON><PERSON>", "code": "XYI", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Norrkoping Railway Service", "code": "XYK", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Kristinehamn Rail", "code": "XYN", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Avesta Krylbo", "code": "XYP", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Angelholm Helsingborg Angelholm Railway Svc", "code": "XYQ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Sala", "code": "XYX", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Arvika Rail Station", "code": "XYY", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Harnosand Rail Station", "code": "XYZ", "stateCode": "", "countryCode": "SE", "countryName": "Sweden"}, {"name": "Metz Railway Station", "code": "XZI", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Macau Ferry", "code": "XZM", "stateCode": "", "countryCode": "MO", "countryName": "Macau"}, {"name": "Avignon Railway Station", "code": "XZN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Oslo Central Station", "code": "XZO", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Pass Generic", "code": "XZP", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Rail Generic", "code": "XZR", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YAA", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Cat Lake", "code": "YAC", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Frances Municipal", "code": "YAG", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YAK", "stateCode": "AK", "countryCode": "US", "countryName": "United States"}, {"name": "Sault Ste Marie", "code": "YAM", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>p", "code": "YAP", "stateCode": "", "countryCode": "FM", "countryName": "Micronesia"}, {"name": "Attawapiskat", "code": "YAT", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Angling Lake", "code": "YAX", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "St Anthony", "code": "YAY", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Tofino Airport", "code": "YAZ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>aru<PERSON>", "code": "YBB", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YBC", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Bagotville", "code": "YBG", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Black Tickle", "code": "YBI", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "YBK", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Campbell River", "code": "YBL", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YBP", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "<PERSON>", "code": "YBR", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Brochet", "code": "YBT", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Berens River", "code": "YBV", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Bedwell Harbor", "code": "YBW", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "YBX", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Cambridge Bay", "code": "YCB", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YCD", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Castlegar", "code": "YCG", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Colville Lake", "code": "YCK", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Coppermine Kugluktuk", "code": "YCO", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Cross Lake", "code": "YCR", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Chesterfield Inlet", "code": "YCS", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Clyde River", "code": "YCY", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Dawson City", "code": "YDA", "stateCode": "YT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Deer Lake", "code": "YDF", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YDN", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>in", "code": "YDP", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Dawson Creek", "code": "YDQ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Edmonton", "code": "YEA", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Edmonton International", "code": "YEG", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YEI", "stateCode": "", "countryCode": "TR", "countryName": "Turkey"}, {"name": "Arviat", "code": "YEK", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Severn", "code": "YER", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "YES", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Inuvik <PERSON>", "code": "YEV", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Albany", "code": "YFA", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Iqaluit", "code": "YFB", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fredericton", "code": "YFC", "stateCode": "NB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Hope", "code": "YFH", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Snare Lake", "code": "YFJ", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>lin Flon", "code": "YFO", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Simpson", "code": "YFS", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fox Harbour St Lewis", "code": "YFX", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gillies Bay", "code": "YGB", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ganges Harbor", "code": "YGG", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Good Hope", "code": "YGH", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YGJ", "stateCode": "", "countryCode": "JP", "countryName": "Japan"}, {"name": "Kingston", "code": "YGK", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "La Grande", "code": "YGL", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gods Narrows", "code": "YGO", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Iles De <PERSON> Madeleine", "code": "YGR", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Igloolik", "code": "YGT", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Havre St Pierre", "code": "YGV", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kuujjuarapik", "code": "YGW", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YGX", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YGZ", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Port Hope Simpson", "code": "YHA", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Dryden Municipal", "code": "YHD", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Charlottetown", "code": "YHG", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YHI", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gjoa Haven", "code": "YHK", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "YHM", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Hopedale", "code": "YHO", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Poplar Hill", "code": "YHP", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ch<PERSON><PERSON>", "code": "YHR", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YHS", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Montreal St Hubert", "code": "YHU", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Hay River", "code": "YHY", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Halifax Stanfield International Airport", "code": "YHZ", "stateCode": "NS", "countryCode": "CA", "countryName": "Canada"}, {"name": "Pakuashipi", "code": "YIF", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yichang", "code": "YIH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Ivujivik", "code": "YIK", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YIN", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Pond Inlet", "code": "YIO", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Detroit Willow Run", "code": "YIP", "stateCode": "MI", "countryCode": "US", "countryName": "United States"}, {"name": "Garden Hill Island Lk", "code": "YIV", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YIW", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Stephenville", "code": "YJT", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kamloops", "code": "YKA", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kitchener Waterloo International Airport", "code": "YKF", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YKG", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sc<PERSON>fferville", "code": "YKL", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yakima Air Terminal", "code": "YKM", "stateCode": "WA", "countryCode": "US", "countryName": "United States"}, {"name": "Waskaganish", "code": "YKQ", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yakutsk", "code": "YKS", "stateCode": "", "countryCode": "RU", "countryName": "Russia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YKT", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "YKU", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lake Harbour Kimmirut", "code": "YLC", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lac La Martre Wha Ti", "code": "YLE", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lansdowne House", "code": "YLH", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lloydminster", "code": "YLL", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YLW", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Langley Regional", "code": "YLY", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Marys Harbour", "code": "YMH", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Mcmurray", "code": "YMM", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Makkovik", "code": "YMN", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Moosonee", "code": "YMO", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Montreal", "code": "YMQ", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Chibougamau", "code": "YMT", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Montreal Mirabel", "code": "YMX", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "YNA", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yanbu", "code": "YNB", "stateCode": "", "countryCode": "SA", "countryName": "Saudi Arabia"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YNC", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ottawa Gatineau", "code": "YND", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Norway House", "code": "YNE", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Youngstown", "code": "YNG", "stateCode": "OH", "countryCode": "US", "countryName": "United States"}, {"name": "Yanji", "code": "YNJ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "North Spirit Lake", "code": "YNO", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Nemiscau", "code": "YNS", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yan<PERSON>", "code": "YNT", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Yangyang", "code": "YNY", "stateCode": "", "countryCode": "KR", "countryName": "South Korea"}, {"name": "Yancheng", "code": "YNZ", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Old Crow", "code": "YOC", "stateCode": "YT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ogoki", "code": "YOG", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Oxford House", "code": "YOH", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "High Level Footner Lake", "code": "YOJ", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Rainbow Lake", "code": "YOP", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ottawa International", "code": "YOW", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Port Alberni", "code": "YPB", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Paulatuk", "code": "YPC", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Peace River", "code": "YPE", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "YPH", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Aupaluk", "code": "YPJ", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Pickle Lake", "code": "YPL", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Pikangikum", "code": "YPM", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Peawanuck", "code": "YPO", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Prince <PERSON>", "code": "YPR", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Powell River", "code": "YPW", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Povungnituk Puvirnituq", "code": "YPX", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Quebec", "code": "YQB", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Quaqtaq", "code": "YQC", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "The Pas", "code": "YQD", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Red Deer", "code": "YQF", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Windsor", "code": "YQG", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YQK", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lethbridge", "code": "YQL", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Greater Moncton International Airport", "code": "YQM", "stateCode": "NB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YQN", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Comox", "code": "YQQ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Regina", "code": "YQR", "stateCode": "SK", "countryCode": "CA", "countryName": "Canada"}, {"name": "Thunder Bay", "code": "YQT", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Grande Prairie", "code": "YQU", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gander", "code": "YQX", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sydney", "code": "YQY", "stateCode": "NS", "countryCode": "CA", "countryName": "Canada"}, {"name": "Quesnel", "code": "YQZ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Rae <PERSON>", "code": "YRA", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Resolute", "code": "YRB", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YRF", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Rigolet", "code": "YRG", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YRJ", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Red Lake", "code": "YRL", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Red Sucker Lake", "code": "YRS", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YRT", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sudbury", "code": "YSB", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Snowdrift Lutselke", "code": "YSG", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Saint <PERSON>", "code": "YSJ", "stateCode": "NB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sanikiluaq", "code": "YSK", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Smith", "code": "YSM", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Postville", "code": "YSO", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Nanisivik", "code": "YSR", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Ste Therese Point", "code": "YST", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sachs Harbour", "code": "YSY", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Cape Dorset", "code": "YTE", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "YTH", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Big Trout", "code": "YTL", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Guildwood Toronto", "code": "YTO", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Tasiujuaq", "code": "YTQ", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YTS", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Toronto Island", "code": "YTZ", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Tuktoyaktuk", "code": "YUB", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Umiujaq", "code": "YUD", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Montreal <PERSON>", "code": "YUL", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yuma International", "code": "YUM", "stateCode": "AZ", "countryCode": "US", "countryName": "United States"}, {"name": "Repulse Bay", "code": "YUT", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Hall Beach", "code": "YUX", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YUY", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YVA", "stateCode": "", "countryCode": "KM", "countryName": "Comoros"}, {"name": "Bonaventure", "code": "YVB", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Qikiqtarjuaq", "code": "YVM", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Val DOr", "code": "YVO", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "YVP", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON>", "code": "YVQ", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Vancouver Intl", "code": "YVR", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Deer Lake", "code": "YVZ", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kangiqsujuaq", "code": "YWB", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Winnipeg", "code": "YWG", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Victoria Inner Harbor", "code": "YWH", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Deline", "code": "YWJ", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YWK", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Williams <PERSON>", "code": "YWL", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Williams Harbour", "code": "YWM", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Webequie", "code": "YWP", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON>", "code": "YWS", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Cranbrook", "code": "YXC", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Saskatoon", "code": "YXE", "stateCode": "SK", "countryCode": "CA", "countryName": "Canada"}, {"name": "Medicine Hat", "code": "YXH", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort St John", "code": "YXJ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "YXK", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sioux Lookout", "code": "YXL", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Whale Cove", "code": "YXN", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Pangnirtung", "code": "YXP", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Prince <PERSON>", "code": "YXS", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Terrace", "code": "YXT", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "London International", "code": "YXU", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Abbotsford", "code": "YXX", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Whitehorse", "code": "YXY", "stateCode": "YT", "countryCode": "CA", "countryName": "Canada"}, {"name": "North Bay", "code": "YYB", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Calgary International", "code": "YYC", "stateCode": "AB", "countryCode": "CA", "countryName": "Canada"}, {"name": "<PERSON><PERSON>", "code": "YYD", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Nelson", "code": "YYE", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Pen<PERSON><PERSON>", "code": "YYF", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Charlottetown", "code": "YYG", "stateCode": "PE", "countryCode": "CA", "countryName": "Canada"}, {"name": "Taloyoak", "code": "YYH", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Victoria International", "code": "YYJ", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Lynn Lake", "code": "YYL", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Goose Bay", "code": "YYR", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "St Johns", "code": "YYT", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kapuskasing", "code": "YYU", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Mont <PERSON>", "code": "YYY", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Toronto Pearson International", "code": "YYZ", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Yellowknife", "code": "YZF", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Salluit", "code": "YZG", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sandspit", "code": "YZP", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sarnia", "code": "YZR", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Coral Harbour", "code": "YZS", "stateCode": "NU", "countryCode": "CA", "countryName": "Canada"}, {"name": "Port Hardy", "code": "YZT", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Sept Iles", "code": "YZV", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "York Landing", "code": "ZAC", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Zadar", "code": "ZAD", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Zagreb Pleso", "code": "ZAG", "stateCode": "", "countryCode": "HR", "countryName": "Croatia"}, {"name": "Zahedan", "code": "ZAH", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Valdivia Pichoy", "code": "ZAL", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Zamboanga", "code": "ZAM", "stateCode": "", "countryCode": "PH", "countryName": "Philippines"}, {"name": "Nuremberg HBF Railway Service", "code": "ZAQ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Zhaotong", "code": "ZAT", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Zaragoza", "code": "ZAZ", "stateCode": "", "countryCode": "ES", "countryName": "Spain"}, {"name": "Basel Mulhouse Basil Bad   Railway", "code": "ZBA", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Bathurst", "code": "ZBF", "stateCode": "NB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Biloela", "code": "ZBL", "stateCode": "QL", "countryCode": "AU", "countryName": "Australia"}, {"name": "<PERSON><PERSON>", "code": "ZBR", "stateCode": "", "countryCode": "IR", "countryName": "Iran"}, {"name": "Aschaffenburg", "code": "ZCB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Baden Baden", "code": "ZCC", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Zacatecas La Calera", "code": "ZCL", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Temuco", "code": "ZCO", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Basel Mulhouse SBB Railway Service", "code": "ZDH", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Brno Bus Service", "code": "ZDN", "stateCode": "", "countryCode": "CZ", "countryName": "Czech Republic"}, {"name": "Dundee ScotRail", "code": "ZDU", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "<PERSON><PERSON>", "code": "ZEE", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Garmisch Partenkirchen", "code": "ZEI", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Gelsenkirchen Off line Point", "code": "ZEJ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON>", "code": "ZEL", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "East Main", "code": "ZEM", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "London Rail", "code": "ZEP", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Goettingen", "code": "ZEU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>lo<PERSON>", "code": "ZEX", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Chesterfield Bus Service", "code": "ZFI", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Gare de Rennes", "code": "ZFJ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Fort Mcpherson", "code": "ZFM", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Fort Norman Tulita", "code": "ZFN", "stateCode": "NT", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gare de Bordeaux", "code": "ZFQ", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Philadelphia 30st Rail Station", "code": "ZFV", "stateCode": "PA", "countryCode": "US", "countryName": "United States"}, {"name": "Glasgow ScotRail", "code": "ZGG", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Copenhagen Rail Station", "code": "ZGH", "stateCode": "", "countryCode": "DK", "countryName": "Denmark"}, {"name": "Gods River", "code": "ZGI", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Zhongshan Ferry Port", "code": "ZGN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Gotha Railway", "code": "ZGO", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "ZGS", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Gaua", "code": "ZGU", "stateCode": "", "countryCode": "VU", "countryName": "Vanuatu"}, {"name": "Zhanjiang", "code": "ZHA", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Zig<PERSON><PERSON>", "code": "ZIG", "stateCode": "", "countryCode": "SN", "countryName": "Senegal"}, {"name": "Ixtapa Zihuatanejo Internacional", "code": "ZIH", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Inverness ScotRail", "code": "ZIV", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Kaschechewan", "code": "ZKE", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Kegaska", "code": "ZKG", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Le Mans Rail Station", "code": "ZLN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Manzanillo", "code": "ZLO", "stateCode": "", "countryCode": "MX", "countryName": "Mexico"}, {"name": "Liverpool Street Stn Railway Service", "code": "ZLS", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "La Tabatiere", "code": "ZLT", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Hamburg Railway Service", "code": "ZMB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Newark Metropark Rail", "code": "ZME", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "Magdeburg Railway Service", "code": "ZMG", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Masset", "code": "ZMT", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Munich HBF Railway Service", "code": "ZMU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Nanaimo Harbour", "code": "ZNA", "stateCode": "BC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Hamm", "code": "ZNB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON>", "code": "ZNE", "stateCode": "WA", "countryCode": "AU", "countryName": "Australia"}, {"name": "Ingolstadt", "code": "ZNQ", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Ko<PERSON>nz", "code": "ZNV", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Limburg", "code": "ZNW", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Zanzibar Kisauni", "code": "ZNZ", "stateCode": "", "countryCode": "TZ", "countryName": "Tanzania"}, {"name": "Lueneburg Railway Service", "code": "ZOG", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Osorno Canal Balo", "code": "ZOS", "stateCode": "", "countryCode": "CL", "countryName": "Chile"}, {"name": "Oberhausen", "code": "ZOY", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Offenburg", "code": "ZPA", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Sachigo Lake", "code": "ZPB", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Regensburg Railway Service", "code": "ZPM", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Siegburg", "code": "ZPY", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Queenstown Frankton", "code": "ZQN", "stateCode": "", "countryCode": "NZ", "countryName": "New Zealand"}, {"name": "Wolfsburg", "code": "ZQU", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Saarbruecken", "code": "ZQW", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Frankfurt HBF Railway Service", "code": "ZRB", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Zurich", "code": "ZRH", "stateCode": "", "countryCode": "CH", "countryName": "Switzerland"}, {"name": "Round Lake", "code": "ZRJ", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Newark NJ Rail", "code": "ZRP", "stateCode": "NJ", "countryCode": "US", "countryName": "United States"}, {"name": "San Salvador", "code": "ZSA", "stateCode": "", "countryCode": "BS", "countryName": "Bahamas"}, {"name": "St Pierre dela Reunion", "code": "ZSE", "stateCode": "", "countryCode": "RE", "countryName": "Reunion"}, {"name": "Sandy Lake", "code": "ZSJ", "stateCode": "ON", "countryCode": "CA", "countryName": "Canada"}, {"name": "Stendal Railway Service", "code": "ZSN", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "<PERSON><PERSON><PERSON>", "code": "ZTA", "stateCode": "", "countryCode": "PF", "countryName": "French Polynesia"}, {"name": "Tete a La Baleine", "code": "ZTB", "stateCode": "QC", "countryCode": "CA", "countryName": "Canada"}, {"name": "Westchester County Stamford Rail STN", "code": "ZTF", "stateCode": "NY", "countryCode": "US", "countryName": "United States"}, {"name": "Zakinthos Is", "code": "ZTH", "stateCode": "", "countryCode": "GR", "countryName": "Greece"}, {"name": "Shamattawa", "code": "ZTM", "stateCode": "MB", "countryCode": "CA", "countryName": "Canada"}, {"name": "Zhuhai Airport", "code": "ZUH", "stateCode": "", "countryCode": "CN", "countryName": "China"}, {"name": "Churchill Falls", "code": "ZUM", "stateCode": "NL", "countryCode": "CA", "countryName": "Canada"}, {"name": "New Haven Rail", "code": "ZVE", "stateCode": "CT", "countryCode": "US", "countryName": "United States"}, {"name": "Savannakhet", "code": "ZVK", "stateCode": "", "countryCode": "LA", "countryName": "Laos"}, {"name": "Hanover Railway", "code": "ZVR", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Wilmington Rail", "code": "ZWI", "stateCode": "DE", "countryCode": "US", "countryName": "United States"}, {"name": "Stuttgart Railway Service", "code": "ZWS", "stateCode": "", "countryCode": "DE", "countryName": "Germany"}, {"name": "Aberdeen ScotRail", "code": "ZXA", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Edinburgh ScotRail", "code": "ZXE", "stateCode": "", "countryCode": "GB", "countryName": "United Kingdom"}, {"name": "Roervik", "code": "ZXF", "stateCode": "", "countryCode": "NO", "countryName": "Norway"}, {"name": "Amsterdam Railway Service", "code": "ZYA", "stateCode": "", "countryCode": "NL", "countryName": "Netherlands"}, {"name": "Sylhet Civil", "code": "ZYL", "stateCode": "", "countryCode": "BD", "countryName": "Bangladesh"}, {"name": "Nimes Railway", "code": "ZYN", "stateCode": "", "countryCode": "FR", "countryName": "France"}, {"name": "Brussels Midi Railway Station", "code": "ZYR", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}, {"name": "Antwerp Berchem Railway Stn", "code": "ZYZ", "stateCode": "", "countryCode": "BE", "countryName": "Belgium"}]