["Aberdeen, SD (ABR-Aberdeen)", "Abilene, TX (ABI-Abilene Regional)", "Acapulco, Mexico (ACA-Juan <PERSON>)", "Adak Island, AK (ADK-Adak Island)", "Aguascalientes, Mexico (AGU-Aguascalientes)", "Akron, OH (CAK-Akron/Canton Regional)", "Albany, GA (ABY-Albany Dougherty County)", "Albany, NY (ALB-Albany County)", "Albuquerque, NM (ABQ-Albuquerque Intl.)", "Alexandria, LA (AEX-Alexandria Intl.)", "Allentown, PA (ABE-Allentown/Bethlehem/Easton)", "Alpena, MI (APN-County Regional)", "Amarillo, TX (AMA-Amarillo Intl.)", "Anchorage, AK (ANC-Anchorage Intl.)", "Aniak, AK (ANI-Aniak)", "Appleton, WI (ATW-Outagamie County)", "Arcata, CA (ACV-Arcata/Eureka)", "Asheville, NC (AVL-Asheville Regional)", "<PERSON>, CO (ASE-Sardy Field)", "Atka, AK (AKB-Atka)", "Atlanta, GA (ATL-Hartsfield Intl.)", "Atlantic City, NJ (ACY-Atlantic City Intl.)", "Augusta, GA (AGS-Augusta Bush Field)", "Austin, TX (AUS-Austin/Bergstrom Intl.)", "Bakersfield, CA (BFL-Bakersfield)", "Baltimore, MD (BWI-Baltimore Washington)", "Bangor, ME (BGR-Bangor Intl.)", "Bar Harbor, ME (BHB-Bar Harbor)", "Barrow, AK (BRW-Wiley Post/Will Rogers)", "Baton Rouge, LA (BTR-Metropolitan)", "Beaumont, TX (BPT-Jefferson County)", "Bellingham, WA (BLI-Bellingham Intl.)", "Bemidji, MN (BJI-Bemidji/Beltrami County)", "Bend, OR (RDM-Bend/Redmond/Roberts Field)", "Bethel, AK (BET-Bethel Municipal)", "Billings, MT (BIL-Logan Intl.)", "Biloxi, MS (GPT-Gulfport Biloxi)", "Binghamton, NY (BGM-<PERSON>)", "Birmingham, AL (BHM-Birmingham Intl.)", "Bismarck, ND (BIS-Bismarck Municipal)", "Bloomington, IL (BMI-Bloomington Normal)", "Boise, ID (BOI-Boise Air Terminal)", "Boston, MA (BOS-Logan Intl.)", "Bozeman, MT (BZN-Gallatin Field)", "<PERSON><PERSON>, M<PERSON> (BRD-Crow Wing)", "Brownsville, TX (BRO-South Padre Island Intl.)", "Brunswick, GA (BQK-Brunswick Glynco Jetport)", "Buffalo, NY (BUF-Buffalo Niagara Intl.)", "Burbank, CA (BUR-Burbank/Glendale/Pasadena)", "Burlington, VT (BTV-Burlington Intl.)", "<PERSON>te, MT (<PERSON><PERSON><PERSON><PERSON>)", "Cabo San Lucas, Mexico (SJD-Los Cabos Intl.)", "Calgary, AB, Canada (YYC-Calgary Intl.)", "Cancun, Mexico (CUN-Cancun Intl.)", "Canton, OH (CAK-Akron/Canton Regional)", "Casper, WY (CPR-Natrona County Intl.)", "Cedar Rapids, IA (CID-Cedar Rapids Municipal)", "Champaign, IL (CMI-University Of Illinois/Willard)", "Charleston, SC (CHS-Charleston Intl.)", "Charleston, WV (CRW-Yeager)", "Charlotte, NC (CLT-Douglas Intl.)", "Charlottesville, VA (CHO-Charlottesville)", "Chattanooga, TN (CHA-Lovell Field)", "Chicago, IL (All Airports)", "Chicago, IL (MDW-Midway)", "Chicago, IL (ORD-O'Hare)", "Chihuahua, Mexico (CUU-Gral Roberto Fierro Villalobos)", "Cincinnati, OH (CVG-Cincinnati N. Ky.)", "Ciudad Del Carmen, Mexico (CME-Ciudad Del Carmen)", "Cleveland, OH (CLE-Hopkins Intl.)", "<PERSON>, <PERSON><PERSON> (COD-Cody)", "Cold Bay, AK (CDB-Cold Bay)", "College Station, TX (CLL-Easterwood)", "Colorado Springs, CO (COS-Colorado Springs)", "Columbia, SC (CAE-Metropolitan)", "Columbus, GA (CSG-Columbus Metropolitan)", "Columbus, MS (GTR-Golden Triangle)", "Columbus, OH (CMH-Port Columbus Intl.)", "Coos Bay, OR (OTH-North Bend Municipal)", "<PERSON><PERSON><PERSON>, AK (CDV-Mu<PERSON><PERSON> Smith)", "Corpus Christi, TX (CRP-Corpus Christi Intl.)", "Cozumel, Mexico (CZM-Cozumel Intl.)", "Crescent City, CA (CEC-Crescent City)", "Dallas-Ft. Worth, TX (DFW-Dallas/Fort Worth Intl.)", "Dallas, TX (DAL-Love Field)", "Daytona Beach, FL (DAB-Daytona Beach Intl.)", "Dayton, OH (DAY-Dayton Intl.)", "Denver, CO (DEN-Denver Intl.)", "Des Moines, IA (DSM-Des Moines Intl.)", "Detroit, MI (DTW-Wayne County)", "Devils Lake, ND (DVL-Devils Lake)", "<PERSON><PERSON><PERSON>, AK (DLG-<PERSON><PERSON>ham)", "Dodge City, KS (DDC-Dodge City)", "Dothan, AL (DHN-Dothan)", "Dubuque, IA (DBQ-Dubuque Regional)", "Duluth, MN (DLH-Duluth Intl.)", "<PERSON><PERSON>o, CO (DRO-La Plata Field)", "Dutch Harbor, AK (DUT-Dutch Harbor)", "Eau Claire, WI (EAU-Chippewa Valley Regional)", "Edmonton, AB, Canada (YEG-Edmonton Intl.)", "Elko, NV (EKO-Elko)", "Elmira, NY (ELM-Elmira/Corning Regional)", "El Paso, TX (ELP-El Paso Intl.)", "Erie, PA (ERI-Erie Intl.)", "Eugene, OR (EUG-Mahlon Sweet Field)", "Eureka, CA (ACV-Arcata/Eureka)", "Evansville, IN (EVV-Dress Regional)", "Fairbanks, AK (FAI-Fairbanks Intl.)", "Fargo, ND (FAR-Hector)", "Fayetteville, AR (XNA-Northwest Arkansas Regional)", "Fayetteville, NC (FAY-Fayetteville Municipal)", "Flint, MI (FNT-Bishop Intl.)", "Florence, SC (FLO-Florence)", "Fort Dodge, IA (FOD-Fort Dodge Regional)", "Fort Lauderdale, FL (FLL-Hollywood Intl.)", "Fort Myers, FL (RSW-Southwest Florida Regional)", "Fort Smith, AR (FSM-Fort Smith Regional)", "Fort Walton Beach, FL (VPS-Okaloosa County)", "Fort Wayne, IN (FWA-Baer Field)", "Fredericton, NB, Canada (YFC-Fredericton)", "Fresno, CA (FAT-Fresno Air Terminal)", "Gainesville, FL (GNV-Gainesville)", "Garden City, KS (GCK-Garden City)", "Glacier Bay, AK (GST-Gustavus)", "Glasgow, MT (GGW-Glasgow)", "Glendive, MT (GDV-Dawson Community)", "Grand Forks, ND (GFK-Grand Forks Intl.)", "Grand Junction, CO (GJT-Grand Junction)", "Grand Rapids, MI (GRR-<PERSON>tl.)", "Great Falls, MT (GTF-Great Falls Intl.)", "Green Bay, WI (GRB-Austin Straubel Intl.)", "Greenbrier, WV (LWB-Lewisburg)", "Greensboro, NC (GSO-Piedmont Triad Intl.)", "Greenville, MS (GLH-Greenville Municipal)", "Greenville, SC (GSP-Greenville)", "Guadalajara, Mexico (GDL-<PERSON>)", "Gulfport, MS (GPT-Gulfport Biloxi)", "<PERSON><PERSON>, CO (GUC-County)", "<PERSON><PERSON>, AK (GST-Gustavus)", "Halifax, NS, Canada (YHZ-Halifax Intl.)", "<PERSON>, MI (CMX-Memorial)", "Harlingen, TX (HRL-Valley Intl.)", "Harrisburg, PA (MDT-Harrisburg Intl.)", "Hartford, CT (BDL-Bradley Intl.)", "Hattiesburg, MS (PIB-Hattiesburg/Laurel Regional)", "Havana, Cuba (HAV-Havana)", "Havre, MT (HVR-City County)", "Helena, MT (HLN-Helena Regional)", "Hibbing, MN (HIB-Hibbing/Chisholm)", "<PERSON>, <PERSON> (HOM-Howes)", "Honolulu, HI (HNL-Honolulu Intl.)", "Houston, TX (HOU-Houston Hobby)", "Houston, TX (IAH-Houston Bush Intercontinental)", "Huntington, WV (HTS-Huntington)", "Huntsville, AL (HSV-Madison County)", "Idaho Falls, ID (IDA-Fanning Field)", "Indianapolis, IN (IND-Indianapolis Intl.)", "International Falls, MN (INL-Falls Intl.)", "Islip, NY (ISP-MacArthur)", "Ixtapa, Mexico (ZIH-Ixtapa/Zihuatanejo Intl.)", "Jackson Hole, WY (JAC-Jackson Hole)", "<PERSON>, MS (JAN-<PERSON>tl.)", "Jacksonville, FL (JAX-Jacksonville Intl.)", "Jamestown, ND (JMS-Jamestown)", "<PERSON><PERSON>, AK (JNU-Juneau Intl.)", "Kahului, HI (OGG-Kahului/Maui)", "Kailua, HI (KOA-Keahole)", "Kalamazoo, MI (AZO-Kalamazoo/Battle Creek Intl.)", "Kalispell, MT (FCA-Glacier Park Intl.)", "Kamloops, BC, Canada (YKA-Kamloops)", "Kansas City, MO (MCI-Kansas City Intl.)", "Kearney, NE (EAR-Kearney)", "Kelowna, BC, Canada (YLW-Ellison Field)", "<PERSON><PERSON>, AK (ENA-Kenai)", "Kennewick, WA (PSC-Tri-Cities)", "<PERSON><PERSON><PERSON><PERSON>, AK (KTN-Ketchikan Intl.)", "Key West, FL (EYW-Key West Intl.)", "King Cove, AK (KVC-King Cove)", "<PERSON> Salmon, AK (AKN-King Salmon)", "Kitchener, ON, Canada (YKF-Kitchener)", "Klamath Falls, OR (LMT-Kingsley Field)", "Knoxville, TN (TYS-McG<PERSON>/Tyson)", "Kodiak, AK (ADQ-Kodiak)", "Kona, HI (KOA-Keahole)", "<PERSON><PERSON><PERSON><PERSON>, AK (OTZ-Ralph Wien Memorial)", "La Crosse, WI (LSE-La Crosse Municipal)", "Lafayette, LA (LFT-Lafayette Regional)", "Lake Charles, LA (LCH-Lake Charles Regional)", "Lansing, MI (LAN-Capital City)", "La Paz, Mexico (LAP-La Paz)", "Laredo, TX (LRD-Laredo Intl.)", "Las Vegas, NV (LAS-McCarran Intl.)", "Laurel, MS (PIB-Hattiesburg/Laurel Regional)", "Lawton, OK (LAW-Lawton Municipal)", "Leon, Mexico (BJX-Del Bajio)", "Lewisburg, WV (LWB-Lewisburg)", "Lewiston, ID (LWS-Nez Perce County Regional)", "Lewistown, MT (LWT-Lewistown)", "Lexington, KY (LEX-Blue Grass Field)", "<PERSON>, <PERSON><PERSON> (LBL-Liberal)", "Liberia, Costa Rica (LIR-Liberia)", "Lihue, HI (LIH-Lihue/Kauai)", "Lincoln, NE (LNK-Lincoln Municipal)", "Little Rock, AR (LIT-Adams Field)", "London, ON, Canada (YXU-London Municipal)", "Long Beach, CA (LGB-Long Beach Municipal)", "Longview, TX (GGG-Gregg County)", "Loreto, Mexico (LTO-Loreto)", "Los Angeles, CA (LAX-Los Angeles Intl.)", "Los Cabos, Mexico (SJD-Los Cabos Intl.)", "Louisville, KY (SDF-Standiford Field)", "Lubbock, TX (LBB-Lubbock Intl.)", "Lynchburg, VA (LYH-Lynchburg)", "Macon, GA (MCN-<PERSON>)", "Madison, WI (MSN-Dane County Regional)", "Mammoth Lakes, CA (MMH-Mammoth Lakes)", "Manchester, NH (MHT-Manchester)", "Manzanillo, Mexico (ZLO-Intl. Playa de Oro)", "Marquette, MI (MQT-Marquette County)", "Mason City, IA (MCW-Mason City Municipal)", "Maui, HI (OGG-Kahului/Maui)", "Mazatlan, Mexico (MZT-Gen Rafael <PERSON>)", "McAllen, TX (MFE-Miller Intl.)", "<PERSON>, <PERSON> (MCG-McGrath)", "Medford, OR (MFR-Medford/Jackson County)", "Melbourne, FL (MLB-Melbourne Intl.)", "Memphis, TN (MEM-Memphis Intl.)", "Merida, Mexico (MID-Manuel C<PERSON>nc<PERSON>)", "Meridian, MS (MEI-Meridian)", "Mexico City, Mexico (MEX-Juarez Intl.)", "Miami, FL (MIA-Miami Intl.)", "Midland, TX (MAF-Midland Intl.)", "Miles City, MT (MLS-Miles City)", "Milwaukee, WI (MKE-General Mitchell Intl.)", "Minneapolis, MN (MSP-Minneapolis/St. Paul Intl.)", "Minot, ND (MOT-Minot Intl.)", "Missoula, MT (MSO-Missoula County)", "Mobile, AL (MOB-Mobile Regional)", "Moline, IL (MLI-Quad City)", "Monclova, Mexico (LOV-Monclova)", "Monroe, LA (MLU-Monroe Regional)", "Monterey, CA (MRY-Monterey Peninsula)", "Monterrey, Mexico (MTY-<PERSON>)", "<PERSON>, AL (MGM-Dannelly Field)", "Montreal, QC, Canada (YUL-Dorval)", "<PERSON><PERSON>, CO (MTJ-County)", "Morelia, Mexico (MLM-Morelia)", "Moscow, ID (PUW-Pullman/Moscow Regional)", "Muscle Shoals, AL (MSL-Muscle Shoals Regional)", "Muskegon, MI (MKG-Muskegon County)", "Myrtle Beach, SC (MYR-Myrtle Beach Jetport)", "Nantucket, MA (ACK-Nantucket)", "Naples, FL (APF-Naples)", "Nashville, TN (BNA-Nashville Intl.)", "<PERSON>, AK (NLG-Nelson <PERSON>)", "Newark, NJ (EWR-Newark Intl.)", "New Bern, NC (EWN-New Bern)", "Newburgh, NY (SWF<PERSON>Stewart)", "New Haven, CT (HVN-New Haven)", "New Orleans, LA (MSY-New Orleans Intl.)", "New Stuyahok, AK (KNW-New Stuyahok)", "New York, NY (All Airports)", "New York, NY (EWR-Newark Intl.)", "New York, NY (JFK<PERSON>Kennedy)", "New York, NY (LGA-LaGuardia)", "Nome, AK (OME-Nome)", "Norfolk, VA (ORF-Norfolk Intl.)", "North Bend, OR (OTH-North Bend Municipal)", "North Platte, NE (LBF-North Platte)", "Oakland, CA (OAK-Oakland Intl.)", "Oaxaca, Mexico (OAX-Oaxaca)", "Oklahoma City, OK (OKC-<PERSON>)", "Omaha, NE (OMA-Eppley Field)", "Ontario, CA (ONT-Ontario Intl.)", "Orange County, CA (SNA-Santa Ana)", "Orlando, FL (MCO-Orlando Intl.)", "Ottawa, ON, Canada (YOW-Ottawa Intl.)", "Paducah, KY (PAH-Barkley Regional)", "Palm Springs, CA (PSP-Palm Springs International Airport)", "Panama City, FL (ECP-Bay County)", "Panama City, FL (PFN-Bay County)", "Pasco, WA (PSC-Tri-Cities)", "Pellston, MI (PLN-Emmet County/Mackinac Isl.)", "Pendleton, OR (PDT-Eastern Oregon Regional)", "Pensacola, FL (PNS-Pensacola Regional)", "Peoria, IL (PIA-Greater Peoria)", "Petersburg, AK (PSG-<PERSON>)", "Philadelphia, PA (PHL-Philadelphia Intl.)", "Phoenix, AZ (PHX-Sky Harbor Intl.)", "<PERSON>, SD (PIR-Pierre Regional)", "Pittsburgh, PA (PIT-Greater Pittsburgh Intl.)", "Pocatello, ID (PIH-Pocatello Regional)", "Portland, ME (PWM-Intl. Jet Port)", "Portland, OR (PDX-Portland Intl.)", "Port Moller, AK (PML-Port Moller)", "Providence, RI (PVD-T.F. Green State)", "Prudhoe Bay, AK (SCC-Deadhorse)", "Puebla, Mexico (PBC-Puebla)", "Puerto Vallarta, Mexico (PVR-<PERSON>)", "<PERSON><PERSON><PERSON>, WA (PUW-Pullman/Moscow Regional)", "Quebec, QC, Canada (YQB-Quebec)", "Raleigh, NC (RDU-Raleigh Durham Intl.)", "Rapid City, SD (RAP-Rapid City Regional)", "Redding, CA (RDD-Redding Municipal)", "Redmond, OR (RDM-Bend/Redmond/Roberts Field)", "Regina, SK, Canada (YQR-Regina)", "Reno, NV (RNO-Reno/Tahoe Intl.)", "Rhinelander, WI (RHI-Oneida County)", "Richmond, VA (RIC-Richmond Intl.)", "Riviera <PERSON>, Mexico (PVR<PERSON><PERSON>)", "Roanoke, VA (ROA-Roanoke Regional)", "Rochester, MN (RST-Rochester Intl.)", "Rochester, NY (ROC-Greater Rochester Intl.)", "Sacramento, CA (SMF-Metropolitan)", "Sacramento, CA (SMF-Sacramento Intl.)", "Saginaw, MI (MBS-Tri-City)", "Salisbury-Ocean City, MD (SBY-Salisbury-Ocean City)", "Saltillo, Mexico (SLW-Saltillo)", "Salt Lake City, UT (SLC-Salt Lake City Intl.)", "San Angelo, TX (SJT-Mathis Field)", "San Antonio, TX (SAT-San Antonio Intl.)", "San Diego, CA (SAN-Lindbergh Field)", "Sand Point, AK (SDP-Sand Point)", "San Francisco, CA (SFO-San Francisco Intl.)", "San Jose, CA (SJC-San Jose Intl.)", "San Jose, Costa Rica (SJO-San Jose Juan Santamaria)", "San Jose del Cabo, Mexico (SJD-Los Cabos Intl.)", "San Luis Obispo, CA (SBP-San Luis Obispo)", "San Luis <PERSON>, Mexico (SLP-San Luis Potosi)", "Santa Ana, CA (SNA-Santa Ana)", "Santa Barbara, CA (SBA-Santa Barbara Municipal)", "Santa Rosa, CA (STS-Santa Rosa)", "Sarasota, FL (SRQ-Sarasota/Bradenton)", "Saskatoon, SK, Canada (YXE-Saskatoon)", "Sault Ste Marie, MI (CIU-Chippewa County Intl.)", "Savannah, GA (SAV-Savannah Intl.)", "<PERSON><PERSON><PERSON><PERSON><PERSON>, NE (BFF-<PERSON><PERSON><PERSON><PERSON><PERSON>)", "Seattle, WA (SEA-Seattle/Tacoma Intl.)", "Shreveport, LA (SHV-Shreveport Regional)", "<PERSON>, <PERSON><PERSON> (SDY-Sidney)", "Sioux City, IA (SUX-Sioux Gateway)", "Sioux Falls, SD (FSD-<PERSON>)", "Sitka, AK (SIT-Sitka)", "Sonoma County, CA (STS-Santa Rosa)", "South Bend, IN (SBN-Michiana Regional)", "Spartanburg, SC (GSP-Spartanburg)", "Spokane, WA (GEG-Spokane Intl.)", "Springfield, MO (SGF-Springfield Branson Regional)", "State College, PA (SCE-University Park)", "St. Cloud, MN (STC-St. Cloud Municipal)", "Steamboat Springs, CO (HDN-Hayden)", "St. George Island, AK (STG-St. George Island)", "St. George, UT (SGU-St. George)", "St. Louis, MO (STL-Lambert/St. Louis Intl.)", "St. Paul Island, AK (SNP-St. Paul Island)", "Sun Valley, ID (SUN-Friedman Memorial)", "Syracuse, NY (SYR-Hancock Intl.)", "Tallahassee, FL (TLH-Tallahassee Regional)", "Tampa, FL (TPA-Tampa Intl.)", "Tampico, Mexico (TAM-Gen<PERSON> <PERSON><PERSON>)", "Texarkana, AR (TXK-Texarkana Regional)", "Thief River Falls, MN (TVF-Thief River Falls Regional)", "Thunder Bay, ON, Canada (YQT-Thunder Bay)", "Toledo, OH (TOL-Toledo Express)", "Toluca, Mexico (TLC-Toluca)", "Toronto, ON, Canada (YYZ-Pearson)", "Torreon, Mexico (TRC-Torreon)", "Traverse City, MI (TVC-Cherry Capital)", "Tri-City, TN (TRI-Tri-City Regional)", "Tucson, AZ (TUS-Tucson Intl.)", "Tulsa, OK (TUL-Tulsa Intl.)", "<PERSON><PERSON><PERSON>, MS (TUP-C.D. Lemons Municipal)", "Twin Falls, ID (TWF-Twin Falls)", "Tyler, TX (TYR-Pounds Field)", "Unalakleet, AK (UNK-Unalakleet)", "Urbana, IL (CMI-University Of Illinois/Willard)", "<PERSON><PERSON>, CO (EGE-Eagle County)", "Valdez, AK (VDZ-Valdez)", "Valdosta, GA (VLD-Valdosta Regional)", "Vancouver, BC, Canada (YVR-Vancouver Intl.)", "Veracruz, Mexico (VER-Las Bajadas)", "Victoria, BC, Canada (YYJ-Victoria)", "Victoria, TX (VCT-County Foster)", "Villahermosa, Mexico (VSA-Villahermosa)", "Waco, TX (ACT-Waco Regional)", "Walla Walla, WA (ALW-Walla Walla Regional)", "Washington, DC (All Airports)", "Washington, DC (BWI-Baltimore Washington)", "Washington, DC (DCA-Reagan National)", "Washington, DC (IAD-Dulles)", "Waterloo, IA (ALO-Waterloo Municipal)", "Watertown, SD (ATY-Watertown Municipal)", "Wausau, WI (CWA-Cen. WI Regional)", "Wenatchee, WA (EAT-Pangborn Field)", "West Palm Beach, FL (PBI-Palm Beach Intl.)", "West Yellowstone, MT (WYS-West Yellowstone)", "White Plains, NY (HPN-Westchester Co.)", "Wichita Falls, TX (SPS-Sheppard Air Force Base)", "Wichita, KS (ICT-Continent)", "Wichita, KS (ICT-<PERSON>)", "<PERSON><PERSON><PERSON>, PA (AVP-Wilkes-Barre Scranton Intl.)", "Wilmington, NC (ILM-Wilmington)", "Winnipeg, MB, Canada (YWG-Winnipeg Intl.)", "Wolf Point, MT (OLF-Wolf Point)", "<PERSON><PERSON><PERSON>, AK (WRG-Wrangell)", "Yakima, WA (YKM-Yakima Air Terminal)", "Yakutat, AK (YAK-Yakutat)", "Yosemite, CA (FAT-Fresno Air Terminal)", "Yosemite, CA (MMH-Mammoth Lakes)", "Zihuatanejo, Mexico (ZIH-Ixtapa/Zihuatanejo Intl.)"]