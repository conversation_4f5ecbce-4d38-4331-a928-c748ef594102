var DATA_TRAIN = [
  {
    "id": "4990e37823",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 2,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 24,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/2/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/24/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "2aacc9aeec",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 17,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 19,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/17/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/19/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "f640654d49",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 23,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/23/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "aeff7ef777",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 1,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 16,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/1/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/16/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "fbb7e46a90",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 28,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/28/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "969ccd7826",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 28,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/28/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "ace33e8077",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "02f759f34d",
    "instruction": {
      "Departure City": "Portland",
      "Departure Day": 11,
      "Departure Month": 3,
      "Destination City": "Washington DC",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Washington, DC (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.DepartureDate": "3/11/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "3b22e7a175",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 8,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 17,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/8/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/17/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "b4c502262b",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 11,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 25,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/11/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/25/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "cf1a080120",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 28,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.DepartureDate": "3/28/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "580d7c81ff",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 26,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/26/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "3dd9b333de",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 14,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/14/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "35241c70d5",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 23,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/23/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "83246db3c6",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 22,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 24,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.DepartureDate": "3/22/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/24/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "a212c8b685",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 21,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 26,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/21/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/26/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "819466b85e",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 27,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/27/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "796b660468",
    "instruction": {
      "Departure City": "Portland",
      "Departure Day": 7,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 12,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.DepartureDate": "3/7/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/12/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "c6c34f16fe",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 30,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/30/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "586371b1f6",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 18,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/18/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "a8b89a5058",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 17,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/17/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "6c63f0c1b5",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 29,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/29/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "9fda5d9af3",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "69608b00a8",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 25,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/25/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "1d30b735d0",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 25,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/25/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "a8a12bbe0a",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 10,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 29,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/10/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/29/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "0b01a1ef04",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 28,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/28/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "891a0a13f2",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 31,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/31/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "5277ebcaac",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 14,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 20,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/14/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/20/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "4386284e1b",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 25,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/25/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "bf1cfedfc7",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 30,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/30/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "d669f15abb",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 2,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 21,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/2/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/21/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "4ed5544d4e",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 26,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (DCA-Reagan National)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/26/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "9094e548b7",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 14,
      "Departure Month": 3,
      "Destination City": "Washington DC",
      "One Way or Round Trip": "return flight",
      "Returning Day": 21,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Washington, DC (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/14/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/21/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "01dca96c5e",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 10,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 10,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/10/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/10/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "0f6aab002c",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 27,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/27/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "4172697521",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 1,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 14,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/1/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/14/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "fa0238c8d5",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 24,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/24/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "58e4822584",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 24,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/24/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "692d7cae3d",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 16,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 24,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, ME (PWM-Intl. Jet Port)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/16/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/24/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "0ee1509081",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 25,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 29,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/25/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/29/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "32c2da8146",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Washington DC",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Washington, DC (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "a1792acb8c",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 11,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/11/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "32d97294cc",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 14,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 29,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/14/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/29/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "fc28b4760e",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 19,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/19/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "865689e731",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 9,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 17,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/9/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/17/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "b544862f9b",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 19,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/19/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "e7c187071c",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 4,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 15,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/4/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/15/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "514f008d1c",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 26,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/26/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "8c96e6e713",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 18,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 25,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.DepartureDate": "3/18/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/25/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "a861838f21",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 9,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 15,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/9/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/15/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "9fd47ad466",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 23,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 23,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/23/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/23/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "8149f30128",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 7,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 11,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/7/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/11/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "5252b22081",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 18,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/18/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "d82331da50",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 22,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/22/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "626ed44d96",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 13,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 16,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/13/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/16/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "258ae7ece7",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 1,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 16,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/1/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/16/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "6d1af68539",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 28,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/28/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "44cdc53652",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 31,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/31/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "5076e07e4f",
    "instruction": {
      "Departure City": "Portland",
      "Departure Day": 25,
      "Departure Month": 3,
      "Destination City": "New York",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.DepartureDate": "3/25/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "b809bf758e",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/23/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "048c92e18e",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 14,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 20,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/14/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/20/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "2c7ccbca1c",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 20,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 25,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/20/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/25/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "fadbced5b2",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 29,
      "Departure Month": 3,
      "Destination City": "Washington DC",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Washington, DC (All Airports)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/29/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "227afa1985",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 4,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/4/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "8bba077a67",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 4,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/4/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "55c47bd18b",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 21,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (All Airports)",
      "SearchFields.DepartureDate": "3/21/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "ca0210b50a",
    "instruction": {
      "Departure City": "Portland",
      "Departure Day": 24,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.DepartureDate": "3/24/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "4842e53a1f",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 17,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 26,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (DCA-Reagan National)",
      "SearchFields.DepartureDate": "3/17/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/26/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "bd8a4bd56f",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 25,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/25/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "2634db80c8",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 6,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/6/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "87a78426d9",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "7f79ac1752",
    "instruction": {
      "Departure City": "Boston",
      "Departure Day": 12,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.DepartureDate": "3/12/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "e1650e0816",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 27,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (All Airports)",
      "SearchFields.DepartureDate": "3/27/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "37e71fa1cf",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 2,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.DepartureDate": "3/2/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  }
];
var DATA_TEST = [
  {
    "id": "6b1dc7fd32",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Boston",
      "One Way or Round Trip": "return flight",
      "Returning Day": 22,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Boston, MA (BOS-Logan Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (DCA-Reagan National)",
      "SearchFields.DepartureDate": "2/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "2/22/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "d57722c700",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 2,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 4,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/2/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/4/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "4d294cd9b6",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 30,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 31,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/30/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/31/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "1df35285b6",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 10,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 17,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/10/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/17/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "be55438f5a",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 11,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 26,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/11/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/26/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "7ff14ebf90",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 5,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 13,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/5/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/13/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "be8bb69c37",
    "instruction": {
      "Departure City": "Denver",
      "Departure Day": 15,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 26,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.DepartureDate": "3/15/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/26/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "35de85736d",
    "instruction": {
      "Departure City": "San Diego",
      "Departure Day": 17,
      "Departure Month": 3,
      "Destination City": "Dallas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 21,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.DepartureDate": "3/17/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/21/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "0b6ee61a01",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "Denver",
      "One Way or Round Trip": "return flight",
      "Returning Day": 27,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Denver, CO (DEN-Denver Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/27/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "55a3ca1b20",
    "instruction": {
      "Departure City": "Dallas",
      "Departure Day": 7,
      "Departure Month": 3,
      "Destination City": "Austin",
      "One Way or Round Trip": "return flight",
      "Returning Day": 19,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Dallas, TX (DAL-Love Field)",
      "SearchFields.DepartureDate": "3/7/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/19/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "734476bd6f",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 10,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/10/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "e355aa0f81",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Las Vegas",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "c7368d4748",
    "instruction": {
      "Departure City": "Washington DC",
      "Departure Day": 3,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 19,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Washington, DC (DCA-Reagan National)",
      "SearchFields.DepartureDate": "3/3/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/19/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "98088d941a",
    "instruction": {
      "Departure City": "San Francisco",
      "Departure Day": 19,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 29,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.DepartureDate": "3/19/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/29/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "76c2b530f3",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 11,
      "Departure Month": 3,
      "Destination City": "Portland",
      "One Way or Round Trip": "return flight",
      "Returning Day": 16,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Portland, OR (PDX-Portland Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/11/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/16/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "e792c46d6f",
    "instruction": {
      "Departure City": "New York",
      "Departure Day": 6,
      "Departure Month": 3,
      "Destination City": "San Francisco",
      "One Way or Round Trip": "return flight",
      "Returning Day": 9,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Francisco, CA (SFO-San Francisco Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "New York, NY (JFK-Kennedy)",
      "SearchFields.DepartureDate": "3/6/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/9/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "362536c811",
    "instruction": {
      "Departure City": "Los Angeles",
      "Departure Day": 26,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 28,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.DepartureDate": "3/26/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/28/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "7a908acd3d",
    "instruction": {
      "Departure City": "Las Vegas",
      "Departure Day": 10,
      "Departure Month": 3,
      "Destination City": "Los Angeles",
      "One Way or Round Trip": "return flight",
      "Returning Day": 21,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "Los Angeles, CA (LAX-Los Angeles Intl.)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Las Vegas, NV (LAS-McCarran Intl.)",
      "SearchFields.DepartureDate": "3/10/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/21/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  },
  {
    "id": "6388c9b5f6",
    "instruction": {
      "Departure City": "Austin",
      "Departure Day": 13,
      "Departure Month": 3,
      "Destination City": "San Diego",
      "One Way or Round Trip": "return flight",
      "Returning Day": 30,
      "Returning Month": 3
    },
    "request": {
      "CacheId": "",
      "DiscountCode": "",
      "SaveFields.DepShldrSel": "False",
      "SaveFields.RetShldrSel": "False",
      "SaveFields.SelDepFareCode": "",
      "SaveFields.SelDepOptId": "-1",
      "SaveFields.SelRetFareCode": "",
      "SaveFields.SelRetOptId": "-1",
      "SearchFields.ArrivalCity": "San Diego, CA (SAN-Lindbergh Field)",
      "SearchFields.CabinType": "Coach",
      "SearchFields.DepartureCity": "Austin, TX (AUS-Austin/Bergstrom Intl.)",
      "SearchFields.DepartureDate": "3/13/2017",
      "SearchFields.DiscountCode": "",
      "SearchFields.IsAwardBooking": "false",
      "SearchFields.IsCalendar": "false",
      "SearchFields.NumberOfTravelers": "1",
      "SearchFields.ReturnDate": "3/30/2017",
      "SearchFields.SearchType": "RoundTrip",
      "SearchFields.UpgradeOption": "none",
      "SourcePage": "Search",
      "deals-link": ""
    }
  }
];
