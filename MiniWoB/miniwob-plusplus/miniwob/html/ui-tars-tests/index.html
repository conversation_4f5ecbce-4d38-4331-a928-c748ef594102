<!DOCTYPE html>
<html>
<head>
  <title>UI-TARS Test Suite</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    .test-list {
      list-style: none;
      padding: 0;
    }
    .test-item {
      margin: 15px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      transition: transform 0.2s;
    }
    .test-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .test-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #1a73e8;
    }
    .test-description {
      color: #666;
      margin-bottom: 15px;
      line-height: 1.5;
    }
    .test-link {
      display: inline-block;
      padding: 8px 15px;
      background-color: #1a73e8;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    .test-link:hover {
      background-color: #1557b0;
    }
  </style>
</head>
<body>
  <h1>UI-TARS Test Suite</h1>
  <p>This test suite evaluates UI-TARS's ability to handle various complex UI interaction scenarios.</p>
  
  <ul class="test-list">
    <li class="test-item">
      <div class="test-title">Scroll Test</div>
      <div class="test-description">Tests the agent's ability to find and precisely click on elements that require scrolling to become visible.</div>
      <a href="TaskScroll.html" class="test-link">Start Test</a>
    </li>
    
    <li class="test-item">
      <div class="test-title">Partial Steps Test</div>
      <div class="test-description">Tests the agent's ability to handle actions that should be performed as atomic operations without interruption.</div>
      <a href="TaskPartialSteps.html" class="test-link">Start Test</a>
    </li>
    
    <li class="test-item">
      <div class="test-title">CSS State Test</div>
      <div class="test-description">Tests the agent's ability to detect CSS state changes and respond accordingly to visual state indicators.</div>
      <a href="TaskCSSState.html" class="test-link">Start Test</a>
    </li>
    
    <li class="test-item">
      <div class="test-title">Overlay Test</div>
      <div class="test-description">Tests the agent's ability to handle overlays and popups that block interaction with the main page content.</div>
      <a href="TaskOverlay.html" class="test-link">Start Test</a>
    </li>
    
    <li class="test-item">
      <div class="test-title">Partial Update Test</div>
      <div class="test-description">Tests the agent's ability to detect and respond to partial page updates and dynamic content changes.</div>
      <a href="TaskPartialUpdate.html" class="test-link">Start Test</a>
    </li>
    
    <li class="test-item">
      <div class="test-title">Passive Changes Test</div>
      <div class="test-description">Tests the agent's ability to detect and respond to changes that occur without user interaction.</div>
      <a href="TaskPassiveChanges.html" class="test-link">Start Test</a>
    </li>

    <li class="test-item">
      <div class="test-title">Overlapping Panels Test</div>
      <div class="test-description">Tests the agent's ability to handle layered UI elements that require specific interaction sequences, such as closing one panel before interacting with another.</div>
      <a href="TaskOverlap.html" class="test-link">Start Test</a>
    </li>
  </ul>
</body>
</html>