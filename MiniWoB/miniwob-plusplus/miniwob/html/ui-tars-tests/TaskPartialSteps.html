<!DOCTYPE html>
<html>
<head>
  <title>Search Input Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .search-container {
      margin: 20px 0;
    }
    #searchInput {
      padding: 10px;
      width: 300px;
      border: 1px solid #ccc;
    }
    #searchButton {
      padding: 10px 15px;
      background-color: #4285f4;
      color: white;
      border: none;
      cursor: pointer;
    }
    .success-message, .error-message {
      margin-top: 15px;
      padding: 10px;
      display: none;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
    .error-message {
      background-color: #f8d7da;
      color: #721c24;
    }
    .timer {
      margin-top: 10px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="instructions">
    Please enter "UI-TARS testing" in the search box and click the search button.<br>
    <span class="key-point">Important: The text input must be completed in one click+input operation; do not split it into multiple steps!</span><br>
    You have 60 seconds to complete this task.
  </div>
  
  <div class="search-container">
    <input type="text" id="searchInput" placeholder="Enter search term...">
    <button id="searchButton">Search</button>
  </div>
  
  <div class="timer" id="timer">Time left: 60 seconds</div>
  
  <div class="success-message" id="successMessage">Success! You have correctly completed the search operation.</div>
  <div class="error-message" id="errorMessage">Failure! Incorrect search term or the operation was split.</div>
  
  <script>
    // 目标查询
    const targetQuery = "UI-TARS testing";
    
    // 倒计时
    let timeLeft = 60;
    let timerInterval = setInterval(function() {
      timeLeft--;
      document.getElementById('timer').textContent = `Time left: ${timeLeft} seconds`;
      
      if (timeLeft <= 0) {
        clearInterval(timerInterval);
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').textContent = 'Failure! Time is up.';
        document.body.setAttribute('data-success', 'false');
      }
    }, 1000);
    
    // 处理搜索按钮点击
    document.getElementById('searchButton').addEventListener('click', function() {
      const query = document.getElementById('searchInput').value;
      clearInterval(timerInterval);
      
      if (query === targetQuery) {
        document.getElementById('successMessage').style.display = 'block';
        document.getElementById('errorMessage').style.display = 'none';
        document.body.setAttribute('data-success', 'true');
      } else {
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').textContent = 'Failure! Incorrect search term.';
        document.getElementById('successMessage').style.display = 'none';
        document.body.setAttribute('data-success', 'false');
      }
    });
    
    // 跟踪输入焦点事件以检测代理是否分割动作
    let focusCount = 0;
    document.getElementById('searchInput').addEventListener('focus', function() {
      focusCount++;
      // 如果代理多次聚焦，可能是在分割动作
      if (focusCount > 1) {
        console.log("Multiple focus events detected - the action might be split.");
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').textContent = 'Warning: Multiple click operations detected. Please complete the input in one operation.';
      }
    });
    
    // 跟踪输入事件
    let inputEvents = 0;
    let lastInputTime = 0;
    document.getElementById('searchInput').addEventListener('input', function() {
      const now = Date.now();
      inputEvents++;
      
      // 检测输入事件之间的时间间隔
      if (lastInputTime > 0 && now - lastInputTime > 1000) {
        console.log("Input events interval too long - input might be split.");
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').textContent = 'Warning: Input pause detected. Please complete the input in one operation.';
      }
      
      lastInputTime = now;
    });
  </script>
</body>
</html>
