<!DOCTYPE html>
<html>
<head>
  <title>Partial Update Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    .title {
      font-size: 24px;
      margin: 0;
    }
    .sort-container {
      position: relative;
      display: inline-block;
    }
    .sort-button {
      padding: 8px 16px;
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
    }
    .sort-button:hover {
      background-color: #f1f3f4;
    }
    .sort-button:after {
      content: '';
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #555;
      margin-left: 8px;
    }
    .sort-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      z-index: 10;
      min-width: 150px;
      display: none;
    }
    .sort-option {
      padding: 10px 15px;
      cursor: pointer;
      font-size: 14px;
    }
    .sort-option:hover {
      background-color: #f5f5f5;
    }
    .sort-option.active {
      background-color: #e8f0fe;
      color: #1a73e8;
    }
    .product-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }
    .product-card {
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .product-image {
      width: 100%;
      height: 180px;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #aaa;
    }
    .product-info {
      padding: 15px;
    }
    .product-name {
      margin: 0 0 10px 0;
      font-size: 16px;
    }
    .product-price {
      font-weight: bold;
      color: #1a73e8;
      margin: 0;
    }
    .product-rating {
      color: #f57c00;
      margin: 5px 0;
    }
    .verify-button {
      margin-top: 20px;
      padding: 10px 20px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    .verify-button:hover {
      background-color: #3367d6;
    }
    .success-message, .error-message {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
    .error-message {
      background-color: #f8d7da;
      color: #721c24;
    }
    .current-sort {
      margin-top: 10px;
      color: #666;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="instructions">
    Task: Click the "Sort" button, select "Price: Low to High" from the dropdown, then click the "Verify" button.<br>
    <span class="key-point">Note: Observe how the product list reorders by price; this is a partial page update!</span>
  </div>
  
  <div class="container">
    <div class="header">
      <h1 class="title">Product List</h1>
      <div class="sort-container">
        <div class="sort-button" id="sortButton">
          Sort: <span id="currentSortText">Relevance</span>
        </div>
        <div class="sort-dropdown" id="sortDropdown">
          <div class="sort-option active" data-sort="relevance">Relevance</div>
          <div class="sort-option" data-sort="price">Price: Low to High</div>
          <div class="sort-option" data-sort="rating">Rating: High to Low</div>
          <div class="sort-option" data-sort="newest">Newest</div>
        </div>
      </div>
    </div>
    
    <div class="current-sort">Current Sort: <span id="sortIndicator">Relevance</span></div>
    
    <div class="product-grid" id="productGrid">
      <!-- 产品将通过JavaScript动态加载 -->
    </div>
    
    <button class="verify-button" id="verifyButton">Verify</button>
    
    <div class="success-message" id="successMessage">Success! You correctly observed the partial update of the product list.</div>
    <div class="error-message" id="errorMessage">Failure! The products are not sorted by price.</div>
  </div>
  
  <script>
    // 产品数据
    const products = [
      { id: 1, name: "Premium Bluetooth Headphones", price: 899, rating: 4.7, image: "headphones.jpg" },
      { id: 2, name: "Smart Watch", price: 1299, rating: 4.5, image: "smartwatch.jpg" },
      { id: 3, name: "Portable Power Bank", price: 199, rating: 4.2, image: "powerbank.jpg" },
      { id: 4, name: "Wireless Charger", price: 149, rating: 4.0, image: "charger.jpg" },
      { id: 5, name: "Bluetooth Speaker", price: 599, rating: 4.6, image: "speaker.jpg" },
      { id: 6, name: "Gaming Mouse", price: 349, rating: 4.8, image: "mouse.jpg" }
    ];
    
    // 全局变量
    let currentSort = "relevance";
    let dropdownVisible = false;
    
    // DOM元素
    const sortButton = document.getElementById('sortButton');
    const sortDropdown = document.getElementById('sortDropdown');
    const productGrid = document.getElementById('productGrid');
    const verifyButton = document.getElementById('verifyButton');
    const currentSortText = document.getElementById('currentSortText');
    const sortIndicator = document.getElementById('sortIndicator');
    
    // 初始化产品列表
    function renderProducts(sortBy) {
      // 清空产品网格
      productGrid.innerHTML = '';
      
      // 复制产品数组以避免修改原始数据
      let sortedProducts = [...products];
      
      // 根据排序方式对产品进行排序
      switch(sortBy) {
        case "price":
          sortedProducts.sort((a, b) => a.price - b.price);
          break;
        case "rating":
          sortedProducts.sort((a, b) => b.rating - a.rating);
          break;
        case "newest":
          // 假设ID越大表示越新
          sortedProducts.sort((a, b) => b.id - a.id);
          break;
        default:
          // 相关性排序 - 使用原始顺序
          break;
      }
      
      // 渲染排序后的产品
      sortedProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        productCard.innerHTML = `
          <div class="product-image">Product Image</div>
          <div class="product-info">
            <h3 class="product-name">${product.name}</h3>
            <p class="product-price">¥${product.price}</p>
            <div class="product-rating">★★★★★ ${product.rating}</div>
          </div>
        `;
        
        productGrid.appendChild(productCard);
      });
    }
    
    // 切换下拉菜单显示/隐藏
    function toggleDropdown() {
      dropdownVisible = !dropdownVisible;
      sortDropdown.style.display = dropdownVisible ? 'block' : 'none';
    }
    
    // 更新排序方式
    function updateSort(sortBy, sortText) {
      currentSort = sortBy;
      currentSortText.textContent = sortText;
      sortIndicator.textContent = sortText;
      
      // 更新下拉菜单中的活动选项
      const options = document.querySelectorAll('.sort-option');
      options.forEach(option => {
        if (option.dataset.sort === sortBy) {
          option.classList.add('active');
        } else {
          option.classList.remove('active');
        }
      });
      
      // 重新渲染产品列表
      renderProducts(sortBy);
      
      // 隐藏下拉菜单
      dropdownVisible = false;
      sortDropdown.style.display = 'none';
    }
    
    // 初始化事件监听器
    function initEventListeners() {
      // 排序按钮点击事件
      sortButton.addEventListener('click', toggleDropdown);
      
      // 排序选项点击事件
      document.querySelectorAll('.sort-option').forEach(option => {
        option.addEventListener('click', function() {
          const sortBy = this.dataset.sort;
          const sortText = this.textContent;
          updateSort(sortBy, sortText);
        });
      });
      
      // 验证按钮点击事件
      verifyButton.addEventListener('click', function() {
        if (currentSort === 'price') {
          document.getElementById('successMessage').style.display = 'block';
          document.getElementById('errorMessage').style.display = 'none';
          document.body.setAttribute('data-success', 'true');
        } else {
          document.getElementById('errorMessage').style.display = 'block';
          document.getElementById('successMessage').style.display = 'none';
          document.body.setAttribute('data-success', 'false');
        }
      });
      
      // 点击页面其他地方关闭下拉菜单
      document.addEventListener('click', function(event) {
        if (dropdownVisible && !sortButton.contains(event.target) && !sortDropdown.contains(event.target)) {
          dropdownVisible = false;
          sortDropdown.style.display = 'none';
        }
      });
    }
    
    // 页面加载时初始化
    window.onload = function() {
      renderProducts(currentSort);
      initEventListeners();
    };
  </script>
</body>
</html>
