<!DOCTYPE html>
<html>
<head>
  <title>Scroll Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      height: 500px;
      overflow-y: auto;
      border: 1px solid #ccc;
      padding: 10px;
    }
    .filter-section {
      margin-bottom: 15px;
    }
    .filter-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .filter-option {
      margin-left: 10px;
      margin-bottom: 5px;
    }
    .target-option {
      background-color: #f8f8f8;
    }
    .success-message {
      display: none;
      color: green;
      font-weight: bold;
      margin-top: 10px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .error-message {
      display: none;
      margin-top: 20px;
      padding: 10px;
      background-color: #fce8e6;
      color: #d93025;
      border-radius: 4px;
    }
    input[type="checkbox"] {
      margin: 0;
      padding: 0;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="instructions">
    <p>Task: Find and check the "256GB" storage option in the filter list.</p>
    <p class="key-point">Note: You'll need to scroll down and click precisely on the checkbox!</p>
  </div>
  <div class="container" id="filterContainer">
    <div class="filter-section">
      <div class="filter-title">Brand</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Apple</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Samsung</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Google</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> OnePlus</div>
    </div>
    
    <div class="filter-section">
      <div class="filter-title">Color</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Black</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> White</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Blue</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Red</div>
    </div>
    
    <div class="filter-section">
      <div class="filter-title">Price Range</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Under $500</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> $500 - $1000</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> Over $1000</div>
    </div>
    
    <!-- Add many filter sections to force scrolling -->
    <div class="filter-section">
      <div class="filter-title">Screen Size</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 5.0" - 5.9"</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 6.0" - 6.4"</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 6.5" - 6.9"</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 7.0" and above</div>
    </div>
    
    <!-- Target section - only visible after scrolling -->
    <div class="filter-section">
      <div class="filter-title">Storage</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 64GB</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 128GB</div>
      <div class="filter-option target-option"><input type="checkbox" id="targetOption"> 256GB</div>
      <div class="filter-option"><input type="checkbox" class="wrong-option"> 512GB</div>
    </div>
  </div>
  
  <div class="success-message" id="successMessage">Success! You found and checked the 256GB option.</div>
  
  <div id="errorMessage" class="error-message">
    Failed! You must click precisely on the 256GB checkbox.
  </div>
  
  <script>
    // Initialize scroll position
    document.getElementById('filterContainer').scrollTop = 0;

    // Track if the task has been completed or failed
    let taskCompleted = false;
    let taskFailed = false;

    // Add event listener for the target checkbox
    document.getElementById('targetOption').addEventListener('change', function(e) {
      if (!taskFailed && e.target.checked) {
        taskCompleted = true;
        document.getElementById('successMessage').style.display = 'block';
        document.getElementById('errorMessage').style.display = 'none';
        document.body.setAttribute('data-success', 'true');
        document.body.removeAttribute('data-fail');
      }
    });

    // Add event listener for any click in the container
    document.getElementById('filterContainer').addEventListener('mousedown', function(e) {
      // If task is already completed or failed, ignore further clicks
      if (taskCompleted || taskFailed) return;

      // Get the target checkbox element
      const targetCheckbox = document.getElementById('targetOption');
      
      // If the click is not directly on the target checkbox
      if (e.target !== targetCheckbox) {
        taskFailed = true;
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('successMessage').style.display = 'none';
        document.body.setAttribute('data-fail', 'true');
        document.body.setAttribute('data-success', 'false');
      }
    }, true); // Use capture phase to catch clicks before they reach checkboxes

    // Prevent scrolling to affect task status
    document.getElementById('filterContainer').addEventListener('scroll', function(e) {
      // Allow scrolling without failing the task
    });
  </script>
</body>
</html>
