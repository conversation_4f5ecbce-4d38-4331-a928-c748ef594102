<!DOCTYPE html>
<html>
<head>
  <title>Form Validation Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .form-container {
      max-width: 400px;
      margin: 0 auto;
      border: 1px solid #eee;
      padding: 20px;
      border-radius: 5px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .required {
      color: red;
    }
    input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .button-wrapper {
      position: relative;
      display: inline-block;
    }
    .button-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      cursor: not-allowed;
    }
    button {
      padding: 10px 15px;
      background-color: #cccccc;
      color: #666666;
      border: none;
      border-radius: 4px;
      transition: all 0.3s ease;
      position: relative;
    }
    button.active {
      background-color: #4285f4;
      color: white;
      cursor: pointer;
    }
    .success-message, .error-message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
    .error-message {
      background-color: #f8d7da;
      color: #721c24;
    }
    .status-indicator {
      margin-top: 15px;
      font-size: 14px;
      color: #666;
    }
    .button-status {
      font-weight: bold;
    }
    .inactive {
      color: #999;
    }
    .active-status {
      color: #4285f4;
    }
  </style>
</head>
<body>
  <div class="instructions">
    Fill out the form and submit.<br>
    <span class="key-point">Note: The submit button is disabled (gray) until all required fields are correctly filled out.</span><br>
    You need to observe the button status, and click it only after it turns blue and becomes clickable.
  </div>
  
  <div class="form-container">
    <form id="testForm">
      <div class="form-group">
        <label>Name <span class="required">*</span></label>
        <input type="text" id="nameInput" required>
      </div>
      
      <div class="form-group">
        <label>Email <span class="required">*</span></label>
        <input type="email" id="emailInput" required placeholder="<EMAIL>">
      </div>
      
      <div class="form-group">
        <div class="button-wrapper">
          <button type="submit" id="submitButton">Submit</button>
          <div class="button-overlay" id="buttonOverlay"></div>
        </div>
      </div>
    </form>
    
    <div class="status-indicator">
      Button status: <span class="button-status inactive" id="buttonStatus">Disabled (Gray)</span>
    </div>
    
    <div class="success-message" id="successMessage">Success! The form has been successfully submitted.</div>
    <div class="error-message" id="errorMessage">Failure! The button is not yet activated. Please fill out all required fields first.</div>
  </div>
  
  <script>
    const nameInput = document.getElementById('nameInput');
    const emailInput = document.getElementById('emailInput');
    const submitButton = document.getElementById('submitButton');
    const buttonOverlay = document.getElementById('buttonOverlay');
    const buttonStatus = document.getElementById('buttonStatus');
    const errorMessage = document.getElementById('errorMessage');
    
    // 记录按钮点击尝试
    let prematureClickAttempts = 0;
    let buttonActive = false;
    
    function validateForm() {
      const nameValue = nameInput.value.trim();
      const emailValue = emailInput.value.trim();
      const emailValid = emailValue !== '' && emailValue.includes('@');
      
      if (nameValue !== '' && emailValid) {
        submitButton.classList.add('active');
        buttonOverlay.style.display = 'none';
        buttonStatus.textContent = 'Active (Blue)';
        buttonStatus.classList.remove('inactive');
        buttonStatus.classList.add('active-status');
        buttonActive = true;
      } else {
        submitButton.classList.remove('active');
        buttonOverlay.style.display = 'block';
        buttonStatus.textContent = 'Disabled (Gray)';
        buttonStatus.classList.remove('active-status');
        buttonStatus.classList.add('inactive');
        buttonActive = false;
      }
    }
    
    // 监听按钮覆盖层的点击
    buttonOverlay.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      prematureClickAttempts++;
      errorMessage.style.display = 'block';
      
      console.log("Premature click attempt:", prematureClickAttempts);
      
      // 如果尝试次数超过限制，标记为失败
      if (prematureClickAttempts >= 2) {
        document.body.setAttribute('data-success', 'false');
        document.body.setAttribute('data-fail', 'true');
        errorMessage.textContent = 'Failure! Multiple attempts to click when the button is not active. The agent failed to correctly recognize the CSS state.';
      }
    });
    
    // 添加事件监听器
    nameInput.addEventListener('input', validateForm);
    emailInput.addEventListener('input', validateForm);
    
    // 处理表单提交
    document.getElementById('testForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      if (!buttonActive) {
        prematureClickAttempts++;
        errorMessage.style.display = 'block';
        return;
      }
      
      document.getElementById('successMessage').style.display = 'block';
      document.getElementById('errorMessage').style.display = 'none';
      document.body.setAttribute('data-success', 'true');
      document.body.removeAttribute('data-fail');
    });
    
    // 初始化表单状态
    validateForm();
  </script>
</body>
</html>
