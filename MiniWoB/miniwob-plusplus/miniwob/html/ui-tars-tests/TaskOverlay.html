<!DOCTYPE html>
<html>
<head>
  <title>Overlay Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .main-container {
      max-width: 600px;
      margin: 0 auto;
      text-align: center;
      padding: 40px;
      border: 1px solid #eee;
      border-radius: 5px;
    }
    .task-button {
      padding: 12px 24px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .task-button:hover {
      background-color: #3367d6;
    }
    .timer {
      margin: 20px 0;
      color: #666;
      font-size: 14px;
    }
    .success-message, .error-message {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
    .error-message {
      background-color: #f8d7da;
      color: #721c24;
    }
    #overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 9999;
    }
    .popup-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      width: 80%;
      text-align: center;
    }
    .popup-title {
      font-size: 20px;
      margin-bottom: 15px;
      color: #333;
    }
    .popup-message {
      margin-bottom: 20px;
      line-height: 1.5;
      color: #555;
    }
    .close-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 20px;
      height: 20px;
      cursor: pointer;
      color: #999;
      font-size: 20px;
      line-height: 20px;
      text-align: center;
      transition: color 0.3s;
    }
    .close-icon:hover {
      color: #333;
    }
    .ad-banner {
      margin-top: 20px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="instructions">
    任务：等待弹窗出现，先关闭弹窗，然后点击"完成任务"按钮。<br>
    <span class="key-point">注意：弹窗将在页面加载后2秒出现，必须先关闭弹窗才能完成任务！</span>
  </div>
  
  <div class="main-container">
    <h2>弹窗测试</h2>
    <p>这个测试评估代理处理弹窗和覆盖层的能力。</p>
    
    <div class="timer" id="timer">弹窗将在 <span id="countdown">2</span> 秒后出现...</div>
    
    <button id="completeTaskBtn" class="task-button">完成任务</button>
    
    <div class="success-message" id="successMessage">成功！您正确地先关闭了弹窗，然后点击了完成按钮。</div>
    <div class="error-message" id="errorMessage">失败！您必须先关闭弹窗，然后才能点击完成按钮。</div>
  </div>
  
  <!-- 弹窗覆盖层 -->
  <div id="overlay">
    <div class="popup-content">
      <div class="close-icon" id="closeOverlayBtn" title="关闭">×</div>
      <div class="popup-title">特别优惠！</div>
      <div class="popup-message">
        立即注册我们的新闻通讯，获取独家优惠和最新更新！
        不要错过这个限时机会。
      </div>
      <div class="ad-banner">
        广告：限时优惠 - 今日特价商品，点击查看详情！
      </div>
    </div>
  </div>
  
  <script>
    // 全局变量
    let overlayVisible = false;
    let countdownInterval;
    let timeoutId;
    
    // 显示弹窗
    function showOverlay() {
      overlayVisible = true;
      document.getElementById('overlay').style.display = 'block';
      document.getElementById('timer').textContent = '弹窗已显示，请先关闭它！';
    }
    
    // 成功完成任务
    function successTask(message) {
      document.getElementById('successMessage').textContent = message;
      document.getElementById('successMessage').style.display = 'block';
      document.getElementById('errorMessage').style.display = 'none';
      document.body.setAttribute('data-success', 'true');
      document.body.removeAttribute('data-fail');
    }
    
    // 任务失败
    function failTask(reason) {
      document.getElementById('errorMessage').textContent = '失败！' + reason;
      document.getElementById('errorMessage').style.display = 'block';
      document.getElementById('successMessage').style.display = 'none';
      document.body.setAttribute('data-fail', 'true');
      document.body.setAttribute('data-success', 'false');
    }
    
    // 初始化倒计时
    function startCountdown() {
      let seconds = 2;
      document.getElementById('countdown').textContent = seconds;
      
      countdownInterval = setInterval(function() {
        seconds--;
        document.getElementById('countdown').textContent = seconds;
        
        if (seconds <= 0) {
          clearInterval(countdownInterval);
          showOverlay();
        }
      }, 1000);
    }
    
    // 监听关闭按钮点击
    document.getElementById('closeOverlayBtn').addEventListener('click', function() {
      overlayVisible = false;
      document.getElementById('overlay').style.display = 'none';
      document.getElementById('timer').textContent = '弹窗已关闭，现在可以点击"完成任务"按钮了！';
    });
    
    // 监听完成任务按钮点击
    document.getElementById('completeTaskBtn').addEventListener('click', function() {
      if (overlayVisible) {
        failTask('您必须先关闭弹窗，然后才能点击完成按钮。');
      } else {
        successTask('成功！您正确地先关闭了弹窗，然后点击了完成按钮。');
      }
    });
    
    // 设置超时失败
    function setupTimeout() {
      timeoutId = setTimeout(function() {
        if (!document.body.hasAttribute('data-success')) {
          failTask('操作超时。您没有在规定时间内完成任务。');
        }
      }, 30000); // 30秒超时
    }
    
    // 页面加载时初始化
    window.onload = function() {
      startCountdown();
      setupTimeout();
    };
  </script>
</body>
</html>
