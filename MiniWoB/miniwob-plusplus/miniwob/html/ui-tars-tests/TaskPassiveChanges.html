<!DOCTYPE html>
<html>
<head>
  <title>Passive Changes Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .app-container {
      max-width: 800px;
      margin: 0 auto;
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
    }
    .app-header {
      background-color: #f5f5f5;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ddd;
    }
    .app-title {
      margin: 0;
      font-size: 20px;
      color: #333;
    }
    .sidebar {
      width: 200px;
      background-color: #f9f9f9;
      padding: 15px;
      border-right: 1px solid #ddd;
      float: left;
      height: 400px;
    }
    .content-area {
      margin-left: 200px;
      padding: 20px;
      min-height: 400px;
    }
    .folder {
      padding: 10px;
      margin-bottom: 5px;
      cursor: pointer;
      border-radius: 4px;
      position: relative;
    }
    .folder:hover {
      background-color: #e9e9e9;
    }
    .folder.active {
      background-color: #e2f1ff;
      font-weight: bold;
    }
    .notification-dot {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 10px;
      height: 10px;
      background-color: #f44336;
      border-radius: 50%;
      display: none;
    }
    .email-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .email-item {
      padding: 12px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    .email-item:hover {
      background-color: #f5f5f5;
    }
    .email-item.unread {
      font-weight: bold;
      background-color: #f0f8ff;
    }
    .email-item .sender {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .email-item .subject {
      color: #333;
    }
    .email-item .preview {
      color: #777;
      font-size: 13px;
      margin-top: 5px;
    }
    .email-item .time {
      color: #999;
      font-size: 12px;
      float: right;
    }
    .timer-container {
      text-align: center;
      margin: 20px 0;
      color: #666;
    }
    .success-message, .error-message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      display: none;
      text-align: center;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
    .error-message {
      background-color: #f8d7da;
      color: #721c24;
    }
    .no-emails {
      color: #999;
      text-align: center;
      margin-top: 50px;
    }
    .email-content {
      display: none;
      padding: 20px;
    }
    .email-header {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .email-subject {
      font-size: 20px;
      margin-bottom: 10px;
    }
    .email-details {
      color: #666;
      font-size: 14px;
    }
    .email-body {
      line-height: 1.6;
    }
    .back-button {
      margin-bottom: 15px;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .back-button:hover {
      background-color: #e9e9e9;
    }
    .clearfix::after {
      content: "";
      clear: both;
      display: table;
    }
  </style>
</head>
<body>
  <div class="instructions">
    Task: A new email will arrive at a random time (within 5-10 seconds). When you see a red notification dot appear on the Inbox, please click "Inbox" to view the new email, then click the new email to complete the task.<br>
    <span class="key-point">Note: You have a total of 20 seconds to view and open the new email!</span>
  </div>
  
  <div class="timer-container">
    <div id="timer">Waiting for new email... Time remaining: <span id="countdown">20</span> seconds</div>
  </div>
  
  <div class="app-container">
    <div class="app-header">
      <h1 class="app-title">Email Client</h1>
    </div>
    
    <div class="clearfix">
      <div class="sidebar">
        <div class="folder active" id="inboxFolder">
          Inbox
          <span class="notification-dot" id="inboxNotification"></span>
        </div>
        <div class="folder">Sent</div>
        <div class="folder">Drafts</div>
        <div class="folder">Spam</div>
        <div class="folder">Deleted</div>
      </div>
      
      <div class="content-area" id="contentArea">
        <div id="emailListView">
          <div class="no-emails" id="noEmails">No new emails</div>
          <ul class="email-list" id="emailList"></ul>
        </div>
        
        <div id="emailContentView" class="email-content">
          <button class="back-button" id="backButton">Back</button>
          <div class="email-header">
            <div class="email-subject" id="emailSubject"></div>
            <div class="email-details" id="emailDetails"></div>
          </div>
          <div class="email-body" id="emailBody"></div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="success-message" id="successMessage">Success! You noticed the new email notification and opened the new email.</div>
  <div class="error-message" id="errorMessage">Failure! You did not complete the task within the allotted time.</div>
  
  <script>
    // 全局变量
    let notificationShown = false;
    let emailOpened = false;
    let timeoutId;
    let countdownInterval;
    let timeLeft = 20;
    
    // 初始化邮件列表
    const initialEmails = [
      {
        id: 'email1',
        sender: '<EMAIL>',
        subject: 'Welcome to the Email Client',
        preview: 'Thank you for using our email client. This is a test environment...',
        time: 'Yesterday',
        unread: false,
        content: 'Thank you for using our email client. This is a test environment used to evaluate UI-TARS’s ability to handle passive changes.'
      },
      {
        id: 'email2',
        sender: '<EMAIL>',
        subject: 'Your Account Update',
        preview: 'We have updated your account settings, details are as follows...',
        time: 'Last week',
        unread: false,
        content: 'We have updated your account settings to enhance security and performance. If you have any questions, please feel free to contact our support team.'
      }
    ];
    
    // 新邮件数据
    const newEmail = {
      id: 'newEmail',
      sender: '<EMAIL>',
      subject: 'Important Notice - Please Read Immediately',
      preview: 'This is an important notification email, please check it as soon as possible...',
      time: 'Just now',
      unread: true,
      content: 'This is an important notification email that requires your immediate attention.\n\nThis email is part of a test environment used to evaluate UI-TARS’s ability to detect passive changes.\n\nSuccessfully opening this email indicates that you have noticed the notification and taken appropriate action.'
    };
    
    // 显示邮件列表
    function renderEmailList(emails) {
      const emailListElement = document.getElementById('emailList');
      const noEmailsElement = document.getElementById('noEmails');
      
      if (emails.length === 0) {
        noEmailsElement.style.display = 'block';
        emailListElement.innerHTML = '';
        return;
      }
      
      noEmailsElement.style.display = 'none';
      emailListElement.innerHTML = '';
      
      emails.forEach(email => {
        const emailElement = document.createElement('li');
        emailElement.className = `email-item${email.unread ? ' unread' : ''}`;
        emailElement.dataset.id = email.id;
        
        emailElement.innerHTML = `
          <div class="time">${email.time}</div>
          <div class="sender">${email.sender}</div>
          <div class="subject">${email.subject}</div>
          <div class="preview">${email.preview}</div>
        `;
        
        emailElement.addEventListener('click', () => openEmail(email));
        
        emailListElement.appendChild(emailElement);
      });
    }
    
    // 打开邮件
    function openEmail(email) {
      const emailListView = document.getElementById('emailListView');
      const emailContentView = document.getElementById('emailContentView');
      const emailSubject = document.getElementById('emailSubject');
      const emailDetails = document.getElementById('emailDetails');
      const emailBody = document.getElementById('emailBody');
      
      emailListView.style.display = 'none';
      emailContentView.style.display = 'block';
      
      emailSubject.textContent = email.subject;
      emailDetails.textContent = `From: ${email.sender} | Time: ${email.time}`;
      emailBody.textContent = email.content;
      
      // 如果是新邮件，标记任务成功
      if (email.id === 'newEmail') {
        emailOpened = true;
        successTask('Success! You noticed the new email notification and opened the new email.');
      }
    }
    
    // 返回邮件列表
    function backToList() {
      const emailListView = document.getElementById('emailListView');
      const emailContentView = document.getElementById('emailContentView');
      
      emailContentView.style.display = 'none';
      emailListView.style.display = 'block';
    }
    
    // 显示新邮件通知
    function showNotification() {
      const inboxNotification = document.getElementById('inboxNotification');
      inboxNotification.style.display = 'block';
      notificationShown = true;
      
      // 直接添加新邮件到列表中，而不是等待点击收件箱
      addNewEmail();
      
      // 更新计时器文本
      document.getElementById('timer').textContent = `New email has arrived! Please check the Inbox. Time remaining: ${timeLeft} seconds`;
    }
    
    // 添加新邮件
    function addNewEmail() {
      const currentEmails = [...initialEmails];
      currentEmails.unshift(newEmail);
      renderEmailList(currentEmails);
    }
    
    // 成功完成任务
    function successTask(message) {
      clearInterval(countdownInterval);
      clearTimeout(timeoutId);
      
      document.getElementById('successMessage').textContent = message;
      document.getElementById('successMessage').style.display = 'block';
      document.getElementById('errorMessage').style.display = 'none';
      document.body.setAttribute('data-success', 'true');
    }
    
    // 任务失败
    function failTask(reason) {
      clearInterval(countdownInterval);
      
      document.getElementById('errorMessage').textContent = `Failure! ${reason}`;
      document.getElementById('errorMessage').style.display = 'block';
      document.getElementById('successMessage').style.display = 'none';
      document.body.setAttribute('data-success', 'false');
      document.body.setAttribute('data-fail', 'true');
    }
    
    // 初始化倒计时
    function startCountdown() {
      countdownInterval = setInterval(function() {
        timeLeft--;
        document.getElementById('countdown').textContent = timeLeft;
        
        if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          if (!emailOpened) {
            failTask('You did not complete the task within the allotted time.');
          }
        }
      }, 1000);
    }
    
    // 页面加载时初始化
    window.onload = function() {
      // 渲染初始邮件列表
      renderEmailList(initialEmails);
      
      // 设置收件箱点击事件 - 现在只需要显示邮件列表
      document.getElementById('inboxFolder').addEventListener('click', function() {
        if (notificationShown) {
          // 点击收件箱后清除通知红点
          document.getElementById('inboxNotification').style.display = 'none';
        }
      });
      
      // 设置返回按钮点击事件
      document.getElementById('backButton').addEventListener('click', backToList);
      
      // 生成随机延迟时间（10-20秒）
      const randomDelay = Math.floor(Math.random() * 10000) + 10000;
      
      // 设置定时器，在随机时间后显示通知
      setTimeout(function() {
        showNotification();
      }, randomDelay);
      
      // 设置60秒超时
      timeoutId = setTimeout(function() {
        if (!emailOpened) {
          failTask('You did not complete the task within the allotted time.');
        }
      }, 60000);
      
      // 启动倒计时
      startCountdown();
    };
  </script>
</body>
</html>
