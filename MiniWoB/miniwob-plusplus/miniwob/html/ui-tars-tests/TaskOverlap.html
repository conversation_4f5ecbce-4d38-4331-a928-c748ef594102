<!DOCTYPE html>
<html>
<head>
  <title>Overlapping Panels Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .instructions {
      margin-bottom: 20px;
      font-weight: bold;
      background-color: #f8f9fa;
      padding: 15px;
      border-left: 4px solid #4285f4;
      line-height: 1.5;
    }
    .key-point {
      color: #d93025;
      font-weight: bold;
    }
    .background-layer {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: transparent;
      cursor: pointer;
    }
    .panel {
      position: absolute;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      width: 300px;
    }
    .departure-panel {
      z-index: 2;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
    }
    .arrival-panel {
      z-index: 1;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
      pointer-events: none;
      opacity: 0.5;
    }
    .input-field {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .panel-title {
      margin-bottom: 15px;
      font-weight: bold;
    }
    .panel-hint {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
    .search-button {
      width: 100%;
      padding: 10px;
      background: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .search-button:hover {
      background: #3367d6;
    }
    .error-message, .success-message {
      display: none;
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
    }
    .error-message {
      background-color: #fce8e6;
      color: #d93025;
    }
    .success-message {
      background-color: #d4edda;
      color: #155724;
    }
  </style>
</head>
<body>
  <div class="instructions">
    <p>Task: First, enter "SFO" in the departure field, then click anywhere outside the panel to close it. 
       The arrival panel will become active. Finally, enter "JFK" and click "Search" to complete the task.</p>
    <p class="key-point">Note: You must close the departure panel before you can interact with the arrival panel!</p>
  </div>

  <div id="backgroundLayer" class="background-layer"></div>

  <div id="departurePanel" class="panel departure-panel">
    <div class="panel-title">Departure Airport</div>
    <input type="text" id="departureInput" class="input-field" placeholder="Enter departure city (e.g., SFO)">
    <div class="panel-hint">Click outside this panel to continue</div>
  </div>

  <div id="arrivalPanel" class="panel arrival-panel">
    <div class="panel-title">Arrival Airport</div>
    <input type="text" id="arrivalInput" class="input-field" placeholder="Enter arrival city (e.g., JFK)">
    <button id="searchButton" class="search-button">Search Flights</button>
  </div>

  <div id="errorMessage" class="error-message">
    Failed! You must close the departure panel first!
  </div>

  <div id="successMessage" class="success-message">
    Success! Flight search initiated from SFO to JFK.
  </div>

  <script>
    let departurePanelActive = true;
    let invalidClickCount = 0;
    
    function isDepartureFilled() {
      return document.getElementById('departureInput').value.trim().toUpperCase() === 'SFO';
    }

    function hideDeparturePanel() {
      document.getElementById('departurePanel').style.display = 'none';
      departurePanelActive = false;
    }

    function showArrivalPanel() {
      const arrivalPanel = document.getElementById('arrivalPanel');
      arrivalPanel.style.opacity = '1';
      arrivalPanel.style.pointerEvents = 'auto';
    }

    function failTask(reason) {
      document.getElementById('errorMessage').textContent = reason;
      document.getElementById('errorMessage').style.display = 'block';
      document.body.setAttribute('data-fail', 'true');
      document.body.setAttribute('data-success', 'false');
    }

    // Background click handler
    document.getElementById('backgroundLayer').addEventListener('click', function(e) {
      if (departurePanelActive && isDepartureFilled()) {
        hideDeparturePanel();
        showArrivalPanel();
      }
    });

    // Arrival panel click handler
    document.getElementById('arrivalPanel').addEventListener('click', function(e) {
      if (departurePanelActive) {
        invalidClickCount++;
        if (invalidClickCount > 2) {
          failTask('Too many attempts to click arrival panel before closing departure panel!');
        }
      }
    });

    // Search button handler
    document.getElementById('searchButton').addEventListener('click', function() {
      const arrivalInput = document.getElementById('arrivalInput').value.trim().toUpperCase();
      
      if (departurePanelActive) {
        failTask('You must close the departure panel first!');
        return;
      }
      
      if (arrivalInput === 'JFK') {
        document.getElementById('successMessage').style.display = 'block';
        document.body.setAttribute('data-success', 'true');
      } else {
        failTask('Please enter "JFK" as the arrival city.');
      }
    });

    // Set timeout for task
    setTimeout(function() {
      if (!document.body.hasAttribute('data-success')) {
        failTask('Time limit exceeded!');
      }
    }, 15000);
  </script>
</body>
</html> 