<!DOCTYPE html>
<html>
<head>
<title>Enter Date Task</title>

<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<style>
#area { margin: 5px; }
input { margin: 5px 0; width: 120px; }
</style>

<!-- JS -->
<script src="../core/core.js"></script>
<script src="../common/ui_utils.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script>
core.EPISODE_MAX_TIME = 20000; // set episode interval to 20 seconds
// since this is just an input and does not require pagination
// like a date picker, expand the date range by a couple of years.
var START_DATE = new Date(2010, 0, 1); // January 1, 2015
var END_DATE = new Date(2019, 11, 31); // December 31, 2016

var genProblem = function() {
  var d = ui_utils.randomDate(START_DATE, END_DATE);
  d3.select('#tt')[0][0].value = '';

  var q = ui_utils.toDateString(d);

  d3.select('#query').html('Enter <span class="bold">' + q + '</span> as the date and hit submit.');

  d3.select('#subbtn').on('click', function(){
    // date comes back in the form of "YYYY-MM-DD", so we'll need to rearrange it.
    var t = d3.select('#tt')[0][0].value.split('-');
    var userDate = t[1] + '/' + t[2] + '/' + t[0];
    var r = userDate === q ? 1.0 : -1.0;
    core.endEpisode(r, r > 0);
  })
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    <div id="form">
      <input type="date" id="tt">
      <button id="subbtn" class="secondary-action">Submit</button>
    </div>
  </div>
</div>
</body>
</html>
