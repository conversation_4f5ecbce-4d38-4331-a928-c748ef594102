<!DOCTYPE html>
<html>
<head>
<title>Button Delay Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/jquery-ui/external/jquery/jquery.js"></script>

<style>
#subbtn { width: 40px; height: 40px; }
#subbtn2 { width: 40px; height: 40px; }
</style>

<script>
var EXPECTED_SEQUENCE = ['subbtn', 'subbtn2'];
var buttonsPushed = [];
var currentTime;
var timeDelay;

var buttonClicked = function(){
  var id = this.getAttribute('id');
  buttonsPushed.push(id);
  if(buttonsPushed.length === 2){
    var correctPushed = buttonsPushed.every(function(v,i){return v==EXPECTED_SEQUENCE[i];});
    var r = correctPushed ? determineReward() : -1.0;
    core.endEpisode(r);
  } else if (buttonsPushed.length === 1){
    currentTime = new Date().getTime();
  }
}

var determineReward = function(){
  var endTime = new Date().getTime();
  var actualDelay = (endTime - currentTime)/1000;
  var diff = Math.abs(actualDelay - timeDelay);
  var percentageDiff = diff/timeDelay;
  console.log({diff: diff, timeDelay: timeDelay, pctDif: percentageDiff})
  // smaller percentage diff is better; means that the button press was closer
  // to the desired time delay.
  if(percentageDiff < 0.15){
    return 1.0;
  } else if (percentageDiff < 0.4){
    return 0.6;
  } else if (percentageDiff < 0.65){
    return 0.35;
  } else if (percentageDiff < 0.85){
    return 0.0;
  } else {
    return -1.0;
  }
}

var genProblem = function() {
  $('button').unbind('click');

  currentTime = undefined;

  timeDelay = core.randi(1,4);
  $('#query').html('Click button ONE, wait ' + timeDelay + ' seconds, then click button TWO.');

  buttonsPushed = [];
  var L = core.randi(0, 118); var U = core.randi(0, 118) + 50;
  var btn = $('#subbtn');
  btn.attr('style', 'position:absolute; left:'+L+'px; top:'+U+'px');
  btn.on('click', buttonClicked);

  var L = core.randi(0, 118); var U = core.randi(0, 118) + 50;
  var btn = $('#subbtn2');
  btn.attr('style', 'position:absolute; left:'+L+'px; top:'+U+'px');
  btn.on('click', buttonClicked);
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query">Click button ONE, then click button TWO.</div>
  <div id="area">
    <button id="subbtn">ONE</button>
    <button id="subbtn2">TWO</button>
  </div>
</div>
</body>
</html>
