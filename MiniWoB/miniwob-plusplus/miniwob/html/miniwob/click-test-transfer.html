<!DOCTYPE html>
<html>
<head>
<title>Click Test Transfer Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../common/ui_utils.js"></script>

<style>
#subbtn { width: 40px; height: 40px; }
#subbtn2 { width: 40px; height: 40px; }
</style>

<script>
var genProblem = function() {
  var correct = (WOB_DATA_MODE == 'test') ? 'TWO' : 'ONE';

  var L = core.randi(0, 118); var U = core.randi(0, 118) + 50;
  var btn = d3.select('#subbtn');
  btn.attr('style', 'position:absolute; left:'+L+'px; top:'+U+'px');
  btn.on('click', null);
  btn.on('click', function(){
    core.endEpisode(correct == 'ONE' ? 1.0 : -1.0, correct == 'ONE');
  });

  var L = core.randi(0, 118); var U = core.randi(0, 118) + 50;
  var btn = d3.select('#subbtn2');
  btn.attr('style', 'position:absolute; left:'+L+'px; top:'+U+'px');
  btn.on('click', null);
  btn.on('click', function(){
    core.endEpisode(correct == 'TWO' ? 1.0 : -1.0, correct == 'TWO');
  });

  d3.select('#query').html('Click button ' + correct + '.');
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    <button id="subbtn">ONE</button>
    <button id="subbtn2">TWO</button>
  </div>
</div>
</body>
</html>
