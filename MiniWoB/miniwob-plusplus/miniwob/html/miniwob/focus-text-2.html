<!DOCTYPE html>
<html>
<head>
<title>Focus Text Task</title>

<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<style>
input { display: block; width: 100px; }
</style>

<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script>
var TEXTBOXES = 3;

var POS = ['1st', '2nd', '3rd'];
var textIds = ['tt1', 'tt2', 'tt3'];

var randomizeInputs = function(){
  // move the text fields around
  for(var i=0;i<TEXTBOXES;i++){
    var marginLeft = 'margin-left:'+core.randi(5,20)+'px;';
    var marginTop = 'margin-top:'+core.randi(5,30)+'px;';
    var textbox = document.getElementById(textIds[i]);
    textbox.setAttribute('style', marginLeft + marginTop);
  }
}

var genProblem = function() {
  randomizeInputs();
  var correctTextbox = core.randi(0,TEXTBOXES);
  d3.select('#query').html('Focus into the ' + POS[correctTextbox] + ' input textbox.');

  // reward awarder
  d3.selectAll('#area input').on('focus', function(){
    this.blur();
    var textid = this.getAttribute('id');
    var r = textid === textIds[correctTextbox] ? 1.0 : -1.0;
    core.endEpisode(r, r > 0);
  });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    <input type="text" id="tt1">
    <input type="text" id="tt2">
    <input type="text" id="tt3">
  </div>
</div>
</body>
</html>
