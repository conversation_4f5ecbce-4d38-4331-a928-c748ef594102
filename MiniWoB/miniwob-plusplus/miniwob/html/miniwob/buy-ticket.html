<!DOCTYPE html>
<html>
<head>
<title>Book Flight Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<link rel="stylesheet" href="../core/jquery-ui/jquery-ui.min.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/jquery-ui/external/jquery/jquery.js"></script>
<script src="../common/ui_utils.js"></script>

<style>
#area { height: 156px; position: relative;
  background-image: -webkit-linear-gradient(top, rgb(207, 223, 238) 0px, rgb(235, 240, 246) 100%); }

.flight { box-shadow: 0 1px 4px rgba(0,0,0,.3); background-color: #FFF;
  width: 140px; padding: 1px; border-radius: 3px; margin: 5px 6px; }
.label-container { display: inline-block; width: 29%; vertical-align: top; }
.label-container label { margin-left: 2px; color: #666; }
.details-container { display: inline-block; width: 69%; margin: 1px 0; }
.details-container div { margin-left: 2px; }

.book { text-align: center; }
.book button { width: 100px; border: 1px solid #B3B3B3; padding: 0px;
  border-radius: 3px; cursor: pointer; font-size: 11px;
  background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0,#fdd774),color-stop(1,#edb82e)); }
.error { border: 1px solid red !important; }
</style>

<script>
// NOTE: A lot of this code was reused from the book-flight task,
// so some variable names/functions might not make a ton of sense
// since they were repurposed for this task.
core.EPISODE_MAX_TIME = 10000;

var START_DATE = new Date(2017, 0, 1); // January 1, 2017
var END_DATE = new Date(2017, 5, 31); // June 30, 2016
var DAY_MILLISECONDS = 86400000;
var MAX_TRIPS = 4;
var DESIRED_TICKET = ['cheapest cost', 'shortest duration', 'longest duration', 'most expensive cost'];
var MAX_PRICE = 2000; // in USD.

var FLIGHT_TEMPLATE = `
<div class="flight-duration">
  <div class="label-container"><label>Duration:</label></div>
  <div class="details-container">
    <div class="time-duration"></div>
  </div>
</div>
<div class="book">
  <button class="buy-ticket">Book for </button>
</div>
`

var createFlightElem = function(result, index){
  var div = document.createElement('div');
  div.setAttribute('class', 'flight');
  div.setAttribute('data-result', index);
  div.innerHTML = FLIGHT_TEMPLATE;

  div.getElementsByClassName('time-duration')[0].innerHTML = result.userDuration;
  div.getElementsByClassName('time-duration')[0].setAttribute('data-duration', result.duration);
  div.getElementsByClassName('buy-ticket')[0].innerHTML += '$' + result.price;
  div.getElementsByClassName('buy-ticket')[0].setAttribute('data-price', result.price);

  $('#area').append(div);
}

var rewardEpisode = function(bookAction, $bought){
  switch(bookAction){
    case 'cheapest cost':
      return cheapestTrip($bought);
    case 'shortest duration':
      return shortestTrip($bought);
    case 'longest duration':
      return longestTrip($bought);
    case 'most expensive cost':
      return expensiveTrip($bought);
  }
}

var determinePrices = function(){
  var expensive = 0;
  var cheapest = MAX_PRICE;
  var tickets = $('.flight');

  for(var i=0;i<tickets.length;i++){
    var priceStr = $(tickets[i]).find('.buy-ticket').attr('data-price');
    var price = parseInt(priceStr, 10);
    cheapest = price < cheapest ? price : cheapest;
    expensive = price > expensive ? price : expensive;
  }
  return {cheapest: cheapest, expensive: expensive};
}

// reward based on whether or not bought ticket
// is the cheapest trip.
var cheapestTrip = function($bought){
  var boughtPriceStr = $bought.find('.buy-ticket').attr('data-price');
  var boughtPrice = parseInt(boughtPriceStr, 10);

  var prices = determinePrices();

  var reward = -1.0
  if(prices.cheapest === boughtPrice) reward = 1.0;
  else if(boughtPrice < prices.expensive) reward = -0.5;
  return reward;
}

// reward based on whether or not bought ticket
// is the most expensive trip.
var expensiveTrip = function($bought){
  var boughtPriceStr = $bought.find('.buy-ticket').attr('data-price');
  var boughtPrice = parseInt(boughtPriceStr, 10);
  var prices = determinePrices();
  var reward = -1.0

  if(prices.expensive === boughtPrice) reward = 1.0;
  else if(boughtPrice > prices.cheapest) reward = -0.5;
  return reward;
}

var determineDurations = function(){
  var longest = 0;
  var shortest = DAY_MILLISECONDS*2;
  var tickets = $('.flight');

  for(var i=0;i<tickets.length;i++){
    var durationStr = $(tickets[i]).find('.time-duration').attr('data-duration');
    var duration = parseInt(durationStr, 10);
    shortest = duration < shortest ? duration : shortest;
    longest = duration > longest ? duration : longest;
  }
  return {shortest: shortest, longest: longest};
}

// reward based on whether or not bought ticket
// is the shortest trip.
var shortestTrip = function($bought){
  var boughtDurationStr = $bought.find('.time-duration').attr('data-duration');
  var boughtDuration = parseInt(boughtDurationStr, 10);
  var durations = determineDurations();
  var reward = -1.0

  // give a shaped reward if they bought one of the medium duration flights.
  if(durations.shortest === boughtDuration) reward = 1.0;
  else if(boughtDuration < durations.longest) reward = -0.5;
  return reward;
}

// reward based on whether or not bought ticket
// is the longest trip.
var longestTrip = function($bought){
  var boughtDurationStr = $bought.find('.time-duration').attr('data-duration');
  var boughtDuration = parseInt(boughtDurationStr, 10);
  var durations = determineDurations();
  var reward = -1.0

  // give a shaped reward if they bought one of the medium duration flights.
  if(durations.longest === boughtDuration) reward = 1.0;
  else if(boughtDuration > durations.shortest) reward = -0.5;
  return reward;
}

var generateFlights = function(day){
  var results = [];
  var totalFlights = MAX_TRIPS; // hard-set this to 5 results.
  var flightStart = new Date(Date.parse(day));
  var flightStartMax = new Date(Date.parse(day) + DAY_MILLISECONDS);
  var flightEnd = new Date(Date.parse(day) + DAY_MILLISECONDS*1.5);
  for(var i=0;i<totalFlights;i++){
    var result = {}
    result.depart = {};
    result.arrive = {};

    var departDate = ui_utils.randomDate(flightStart, flightStartMax);
    var arriveDate = ui_utils.randomDate(departDate, flightEnd);

    result.depart.dateObj = departDate;
    result.depart.day = departDate.toDateString();
    result.depart.time = departDate.toLocaleTimeString().replace(/([\d]+:[\d]{2})(:[\d]{2})(.*)/, "$1$3");

    result.arrive.dateObj = arriveDate;
    result.arrive.day = arriveDate.toDateString();
    result.arrive.time = arriveDate.toLocaleTimeString().replace(/([\d]+:[\d]{2})(:[\d]{2})(.*)/, "$1$3");

    var duration = (arriveDate - departDate)
    result.userDuration = parseInt((duration / (1000*60*60)) % 60) + 'h ' + parseInt((duration / (1000*60)) % 60) + 'm';
    result.duration = duration;
    result.price = core.randi(50, MAX_PRICE);

    results.push(result);
  }
  return results;
}

var genProblem = function(){
  // clear the UI.
  $('#area').empty();

  var expectedDate = ui_utils.randomDate(START_DATE, END_DATE);
  var userDate = ui_utils.toDateString(expectedDate);

  var bookAction = core.sample(DESIRED_TICKET);

  $('#query').html('Buy the ticket with the <span class="bold">' + bookAction + '</span>.');

  var flightResults = generateFlights(userDate);

  var cheapestPrice = MAX_PRICE;
  var shortest = DAY_MILLISECONDS*2;

  for(var i=0;i<flightResults.length; i++){
    createFlightElem(flightResults[i]);
  }

  $('.buy-ticket').on('click', function(){
    var $bought = $(this).parents('.flight');
    var reward = rewardEpisode(bookAction, $bought);
    core.endEpisode(reward, reward > 0);
  });

}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
  </div>
</div>
</body>
</html>
