<!DOCTYPE html>
<html>
<head>
<title>Click Collapsible Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../core/jquery-ui/external/jquery/jquery.js"></script>
<script src="../core/jquery-ui/jquery-ui.min.js"></script>
<script src="../core/jquery-ui-hacks.js"></script>
<script src="../common/ui_utils.js"></script>

<style>
#area h3 { background: #007fff; border: 1px solid #003eff; border-radius: 3px; color: #ffffff; font-weight: normal; margin: 2px; padding: 1px; }
#area div { margin: 2px; }
.alink { text-decoration: underline; color: blue; cursor: pointer; }
.ui-accordion-content { border-width: 0 1px 1px 1px; border-style: solid; padding: 1px; }
</style>

<script>
var createCollapsible = function(div){
  var randomNum = core.randi(1,51);
  var header = '<h3>Section #' + randomNum + '</h3>';
  var html = '<div>' + ui_utils.generateWords(20) + '</div>';
  // place in DOM
  div.append(header + html + '<p style="margin: ' + core.randi(0,40) + 'px ' + core.randi(0,40)
    + 'px;"><br><button id="subbtn" class="secondary-action">Submit</button></p>');
}

var genProblem = function() {
  // generate the task
  var div = $('#area');
  if(div.html().length > 0){ $('#area').accordion('destroy'); }
  div.empty(); // clear previous problem, if any
  $.resetUniqueId();

  createCollapsible(div);

  // generate query text in the UI
  d3.select('#query').html('Expand the section below and click submit.');
  $('#area').accordion({active: false, collapsible: true, animate: false});

  d3.select('#subbtn').on('click', function(){
    var t = $('.ui-accordion-content:visible').length;
    var r = t === 1 ? 1.0 : -1.0;
    core.endEpisode(r, r > 0);
  });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area"></div>
</div>
</body>
</html>
