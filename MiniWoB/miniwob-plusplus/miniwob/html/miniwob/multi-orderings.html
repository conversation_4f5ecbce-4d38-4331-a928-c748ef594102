<!DOCTYPE html>
<html>
<head>
<title>Multi Orderings Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/jquery-ui/external/jquery/jquery.js"></script>
<script src="../common/ui_utils.js"></script>

<script>
core.EPISODE_MAX_TIME = 20000; // 20 seconds
var GENRES = ['Action', 'Adventure', 'Comedy', 'Crime', 'Drama', 'Fantasy', 'Historical', 'Horror', 'Mystery', 'Paranoid', 'Political', 'Romance', 'Saga', 'Satire', 'Surreal', 'Thriller', 'Urban', 'Western'];

var layout3 = function (checker) {
  var genreBox = $('<input type=text>');
  var nameBox = $('<input type=text>');
  var yearBox = $('<input type=text>');
  var rows = [
    $('<tr>').append('<th>Genre</th>').append($('<td>').append(genreBox)),
    $('<tr>').append('<th>Director</th>').append($('<td>').append(nameBox)),
    $('<tr>').append('<th>Year</th>').append($('<td>').append(yearBox)),
    ];
  core.shuffle(rows);
  $('<div class=title-div>Movie Search</div>').appendTo('#area');
  var table = $('<table>').appendTo('#area');
  rows.forEach(function (row) { row.appendTo(table); });
  var submit = $('<div class=final>').text('Submit').appendTo('#area')
    .click(function () {
      checker(genreBox.val(), nameBox.val(), yearBox.val());
    });
  $('#area').append(layout3Style);
}

var layout3Style = `<style>
#area div.title-div { text-align: center; font-weight: bold; margin-top: 10px; font-size: 120%; }
#area table { margin: 5px auto; }
#area th { text-align: right; }
#area input[type=text] { width: 90px; }
#area .final { padding: 2px; border: 1px solid gray; margin: 2px auto; width: 50px; text-align: center; background: #fed; cursor: pointer; }
#area .final:hover { background: #fcc; }
</style>`;

var genProblem = function(){
  var eGenre = core.sample(GENRES).toLowerCase();
  var eName = core.sample(ui_utils.LAST_NAMES);
  var eYear = core.randi(1970, 2018);
  $('#query').html('Search for <b>' + eGenre + '</b> movies directed by <b>' + eName + '</b> from year <b>' + eYear + '</b>.');
  var checker = function (cGenre, cName, cYear) {
    if (cGenre.toLowerCase().trim() == eGenre &&
        cName.toLowerCase().trim() == eName.toLowerCase() &&
        ('' + cYear) == ('' + eYear)) {
      core.endEpisode(1.0, true);
    } else {
      core.endEpisode(-1.0);
    }
  };
  
  $('#area').empty();
  layout3(checker);
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area"></div>
</div>
</body>
</html>
