<!DOCTYPE html>
<html>
<head>
<title>Draw Circle Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v4.min.js"></script>

<style>
  svg { border: 1px dotted black; height: 110px; width: 150px; }
  path { fill: none; stroke: #000; stroke-width: 5px; stroke-linejoin: round; stroke-linecap: round; }
  #controls { text-align: center; }
  #controls button { padding: 8px 10px; }
  #controls button:focus { outline: none; }
</style>

<script>
core.EPISODE_MAX_TIME = 10000;

var startX, startY;
var lastX, lastY;
var arrayPoints = [];

var SUBMIT_TEMPLATE = `
<div id="controls"><button>Submit</button></div>
`

var determineReward = function(stdDev){
  if(stdDev < 2.0) return 1.0
  else if (stdDev < 4.0) return 0.9;
  else if (stdDev < 7.0) return 0.7;
  else if (stdDev < 10.0) return 0.5;
  else if (stdDev < 13.0) return 0.3;
  else if (stdDev < 16.0) return 0.1;
  else if (stdDev < 19.0) return -0.1;
  else if (stdDev < 22.0) return -0.3;
  else if (stdDev < 25.0) return -0.5;
  else if (stdDev < 28.0) return -0.7;
  else if (stdDev < 31.0) return -0.9;
  else return -1.0;
}

var generateCenter = function(){
  // generate a new random grid of shapes
  var svg = d3.select('svg');
  var circle = svg
    .append('circle')
    .attr('id', 'circ')
    .attr('fill', 'green')
    .attr('cx', Math.random() * 100 + 30)
    .attr('cy', Math.random() * 60 + 30)
    .attr('r', 4 + 'px');
}

// currently unused, but let's leave it here for now.
var findCenter = function(arr){
    var minX, maxX, minY, maxY;
    for(var i=0; i< arr.length; i++){
        minX = (arr[i][0] < minX || minX == null) ? arr[i][0] : minX;
        maxX = (arr[i][0] > maxX || maxX == null) ? arr[i][0] : maxX;
        minY = (arr[i][1] < minY || minY == null) ? arr[i][1] : minY;
        maxY = (arr[i][1] > maxY || maxY == null) ? arr[i][1] : maxY;
    }
    return [(minX + maxX) /2, (minY + maxY) /2];
}

var findMarkedCenter = function(){
  var circle = d3.select('#circ')._groups[0][0].getBBox();
  return [circle.x + 4, circle.y +4];
}

var meanDistance = function(arr, center){
  var distances = [];
  var totalDistance = 0;
  for(var i=0; i<arr.length; i++){
    var currPoint = arr[i];
    var d = Math.sqrt( (currPoint[0]-center[0])*(currPoint[0]-center[0]) + (currPoint[1]-center[1])*(currPoint[1]-center[1]) );
    distances.push(d);
    totalDistance += d;
  }

  return {distances: distances, meanDistance: totalDistance/distances.length};
}

var standardDev = function(distances, meanDistance){
  var diffsSquared = 0;
  for(var i=0;i<distances.length;i++){
    var diffSquared = (distances[i] - meanDistance)*(distances[i] - meanDistance)
    diffsSquared += diffSquared;
  }
  var stdDev =  Math.sqrt(diffsSquared/distances.length);
  return stdDev;
}

var findStandardDev = function(points){
  var center = findMarkedCenter();
  var meanDistances = meanDistance(points, center);
  var stdDev = standardDev(meanDistances.distances, meanDistances.meanDistance);
  return stdDev;
}

var drawLine = function(){
  var line = d3.line()
    .curve(d3.curveBasis);

  var svg = d3.select("svg")
    .call(d3.drag()
        .container(function() { return this; })
        .subject(function() { var p = [d3.event.x, d3.event.y]; return [p, p]; })
        .on("start", dragstarted));

  function dragstarted() {
    d3.select('path').remove();
    startX = d3.event.x;
    startY = d3.event.y;
    arrayPoints = [[startX, startY]];

    var d = d3.event.subject,
        active = svg.append("path").datum(d),
        x0 = d3.event.x,
        y0 = d3.event.y;

    d3.event.on("drag", function() {
      var x1 = d3.event.x,
          y1 = d3.event.y,
          dx = x1 - x0,
          dy = y1 - y0;
          lastX = x1;
          lastY = y1;
          arrayPoints.push([x1,y1]);

      if (dx * dx + dy * dy > 100) d.push([x0 = x1, y0 = y1]);
      else d[d.length - 1] = [x1, y1];
      active.attr("d", line);
    });
  }
}

// also adjust some rewards in the event the agent
// draws a small line or box. eg. if it's a path that's
// smaller than 5px height or width, give them -0.25.
var properlySized = function(){
  var pathBox = d3.select('path')._groups[0][0].getBoundingClientRect();
  //console.log({width:  pathBox.width, height: pathBox.height})
  if(pathBox.width < 5 || pathBox.height < 5){
    return -0.25;
  } else if(pathBox.width < 10 || pathBox.height < 10){
    return 0.25;
  } else if (pathBox.width < 20 || pathBox.height < 20){
    return 0.5;
  } else return 1.0;
}

var genProblem = function() {
  d3.select('svg').selectAll('*').remove();

  generateCenter();

  d3.select('#controls button').on('click', function(){
    if(d3.select('path')._groups[0][0] === null) core.endEpisode(-1.0);
    else {
      var stdDev = findStandardDev(arrayPoints);
      var sizeReward = properlySized();
      var circleReward = determineReward(stdDev);
      var reward = sizeReward < 0 ? sizeReward : circleReward * sizeReward;
      core.endEpisode(reward, reward>0);
    }
  });
}

window.onload = function() {
  core.startEpisode();
  drawLine();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query">Draw a circle centered around the marked point by dragging the mouse. Press submit when done.</div>
  <div id="area">
    <svg></svg>
    <div id="controls"><button>Submit</button></div>
  </div>
</div>
</body>
</html>
