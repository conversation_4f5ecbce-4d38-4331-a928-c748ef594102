<!DOCTYPE html>
<html>
<head>
<title>Use Color Wheel Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../core/jscolor.min.js"></script>
<script src="../common/ui_utils.js"></script>

<style>
#area { padding: 10px; }
input { border: 1px solid black; width: 120px; }
#subbtn { margin-top: 20px; }
</style>

<script>
core.EPISODE_MAX_TIME = 7000; // set episode interval to 7 seconds

var genProblem = function() {
  // wipe from previous episde
  //document.getElementById('col').value = '000000';
  //document.getElementById('col').style = 'background-color:black; color:white';

  var ix = core.randi(0, ui_utils.COLORS.length);
  d3.select('#query').html('Select ' + ui_utils.COLORS[ix] + ' with the color picker and hit Submit.');
  var col_desired = ui_utils.colorNameToHex(ui_utils.COLORS[ix]);

  d3.select('#subbtn').on('click', function() {
    var col_selected = '#' + document.getElementById('col').value.toLowerCase();
    var sel = ui_utils.hexToRgb(col_selected);
    var des = ui_utils.hexToRgb(col_desired);

    var r = 1.0 - (Math.abs(sel.r - des.r)/255.0 + Math.abs(sel.g - des.g)/255.0 + Math.abs(sel.b - des.b)/255.0)/3;
    core.endEpisode(r, r > 0);
  });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    Color:<br><input id="col" class="jscolor" data-jscolor="{width:101, height:71, shadow:0, borderWidth:0, backgroundColor:'transparent', insetColor:'#000'}" value="ab2567">
    <button id="subbtn" class="secondary-action">Submit</button>
  </div>
</div>
</body>
</html>
