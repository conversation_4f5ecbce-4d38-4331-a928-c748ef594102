<!DOCTYPE html>
<html>
<head>
<title>Find Midpoint Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../common/shapes.js"></script>

<style>
#area { position: relative; height: 160px; width: 160px; }
#area svg { width: 150px; height: 130px; }
#subbtn { left: 30px; bottom: 5px; position: absolute; }
</style>

<script>
var RADIUS_PADDING = 3; // 3px for circle radius
var QUERY_PADDING = 50; // 50px for #query height

function graphClicked(event) {
  var x = event.pageX;
  var y = event.pageY;
  var divWidth = document.getElementById('svg-grid').clientWidth;
  var divHeight = document.getElementById('svg-grid').clientHeight;

  var svg = d3.select('svg');
  svg.selectAll('.blue').remove();

  shapes.drawCircles([{class: 'blue', 'id': 'blue-circle', cx: x, cy: (y-QUERY_PADDING), r: 3.5, fill: 'blue'}], svg);

  var blueCircle = d3.select('#blue-circle')[0][0].getBBox();
  var blackCircles = d3.selectAll('.black-circle')[0];

  var lines = [];
  for(var i=0;i<blackCircles.length;i++){
    var currentCircle = blackCircles[i].getBBox();
    lines.push({ stroke: 'blue', class: 'blue', x1: (currentCircle.x + RADIUS_PADDING),
      x2: (blueCircle.x + RADIUS_PADDING), y1: (currentCircle.y + RADIUS_PADDING),
      y2: (blueCircle.y + RADIUS_PADDING) })
  }
  shapes.drawLines(lines, svg);
}

var drawAnswer = function(midpoint){
  var svg = d3.select('svg');

  shapes.drawCircles([{class: 'green', id: 'green-circle', cx: midpoint.x + RADIUS_PADDING,
    cy: midpoint.y + RADIUS_PADDING, r: 3.5, fill: 'green'}], svg);

  var greenCircle = d3.select('#green-circle')[0][0].getBBox();
  var blackCircles = d3.selectAll('.black-circle')[0];

  var lines = [];
  for(var i=0; i<blackCircles.length;i++){
    var currentCircle = blackCircles[i].getBBox();
    lines.push({ stroke: 'green', class: 'green', x1: (currentCircle.x + RADIUS_PADDING),
      x2: (greenCircle.x + RADIUS_PADDING), y1: (currentCircle.y + RADIUS_PADDING),
      y2: (greenCircle.y + RADIUS_PADDING) })
  }
  shapes.drawLines(lines, svg);
}

var computeDistances = function(){
  var correctMidpoint = computeMidpoint();
  drawAnswer(correctMidpoint);
  // error handle for submitting without any shapes
  if(d3.select('#blue-circle')[0][0] === null) {
    core.endEpisode(-1, false);
    return;
  }

  var distance = computeShortestPath(correctMidpoint);

  var maxDistance = 30; // max acceptable distance of 30px.
  var score = (maxDistance - distance)/maxDistance;

  // create a scalar if score is within max distance.
  // otherwise reward a negative score.
  if(distance < maxDistance) core.endEpisode(score, true);
  else core.endEpisode(-1, false);
}


var computeMidpoint = function(){
  var blackCircles = d3.selectAll('.black-circle')[0];

  var pointA = blackCircles[0].getBBox();
  var pointB = blackCircles[1].getBBox();

  var midpoint = {};
  midpoint.x = (pointA.x + pointB.x)/2;
  midpoint.y = (pointA.y + pointB.y)/2;

  return midpoint;
}

var computeShortestPath = function(midpoint){
  var userCircle = d3.select('#blue-circle')[0][0].getBBox();
  var xDiff = Math.pow(userCircle.x - midpoint.x, 2);
  var yDiff = Math.pow(userCircle.y - midpoint.y, 2);
  var path = Math.sqrt(xDiff + yDiff);

  return path;
}

var generateCircles = function(){
  var jsonCircles = []
  jsonCircles.push({ cx: core.randi(5,135), cy: core.randi(10,120), fill: 'black', class: 'black-circle', r: 3.5 });

  // go out of our way to generate a second circle, but ensure
  // that it is reasonable spaced out form the first one.
  while(jsonCircles.length < 2){
    var secondCircle = { cx: core.randi(5,135), cy: core.randi(10,125),
      fill: 'black', class: 'black-circle', r: 3.5 };
    var xDiff = Math.abs(secondCircle.cx - jsonCircles[0].cx);
    var yDiff = Math.abs(secondCircle.cy - jsonCircles[0].cy);
    if(xDiff < 30 && yDiff < 30) continue;
    else jsonCircles.push(secondCircle);
  }

  return jsonCircles;
}

var genProblem = function() {
  var svg = d3.select('svg');
  svg.selectAll('*').remove();

  var jsonCircles = generateCircles();
  shapes.drawCircles(jsonCircles, svg);

  svg.on('click', function(){ graphClicked(event); });
  d3.select('#subbtn').on('click', function(){ computeDistances(); });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query">Find and click on the shortest mid-point between the two points, then press submit.</div>
  <div id="area">
    <svg id="svg-grid"></svg>
    <button id="subbtn" class="secondary-action">Submit</button>
  </div>
</div>
</body>
</html>
