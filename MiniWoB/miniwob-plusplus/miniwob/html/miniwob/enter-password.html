<!DOCTYPE html>
<html>
<head>
<title>Enter Password Task</title>

<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<style>
input { margin: 5px; width: 100px; }
#subbtn { margin: 0 7px; }
</style>

<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../common/ui_utils.js"></script>
<script>
core.EPISODE_MAX_TIME = 15000; // set episode interval to 15 seconds

var genProblem = function() {
  d3.select('#password')[0][0].value ='';
  d3.select('#verify')[0][0].value ='';

  var password = ui_utils.generateString(2,6);
  d3.select('#query').html('Enter the password "<span class="bold">' + password + '</span>" into both text fields and press submit.');

  // reward awarder
  d3.select('#subbtn').on('click', function(){
    var p = d3.select('#password')[0][0].value;
    var v = d3.select('#verify')[0][0].value;
    var r = (p === password && v === password) ? 1.0 : -1.0;
    core.endEpisode(r, r > 0);
  });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    <div id="form">
      <p><label>Password</label><input type="password" id="password"></p>
      <p><label>Verify password</label><input type="password" id="verify"></p>
      <button id="subbtn" class="secondary-action">Submit</button>
    </div>
  </div>
</div>
</body>
</html>
