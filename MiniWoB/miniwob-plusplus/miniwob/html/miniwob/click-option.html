<!DOCTYPE html>
<html>
<head>
<title>Click Option Task</title>
<!-- stylesheets -->
<link rel="stylesheet" type="text/css" href="../core/core.css">
<link rel="stylesheet" href="../core/jquery-ui/jquery-ui.min.css">
<!-- JS -->
<script src="../core/core.js"></script>
<script src="../core/d3.v3.min.js"></script>
<script src="../common/ui_utils.js"></script>
<script src="../core/jquery-ui/external/jquery/jquery.js"></script>
<script src="../core/jquery-ui/jquery-ui.min.js"></script>

<style>
input { width: 20px; }
</style>

<script>
var createElements = function(div){
  var n = core.randi(2, 7);
  var clickNames = [];
  for(var i=0;i<n;i++) {
    var chname = ui_utils.generateString(2,8);
    var label = div.append('label')
    label.append('input').attr('type', 'radio').attr('id', 'ch'+i).attr('name', 'radio');
    label[0][0].innerHTML += chname;
    div.append('br');
    clickNames.push(chname);
  }
  var ix = core.randi(0, n);
  var qstr = clickNames[ix];
  return {query: qstr, index: ix};
}

var genProblem = function() {
  var div = d3.select('#boxes');
  div.html('');

  var correctElement = createElements(div);

  d3.select('#query').html('Select ' + correctElement.query + ' and click Submit.');

  d3.select('#subbtn').on('click', function(){
    var r = d3.select('#ch'+correctElement.index)[0][0].checked ? 1.0 : -1.0;
    core.endEpisode(r, r > 0);
  });
}

window.onload = function() {
  core.startEpisode();
}
</script>
</head>
<body>
<div id="wrap">
  <div id="query"></div>
  <div id="area">
    <div id="boxes"></div>
    <br>
    <button id="subbtn" class="secondary-action">Submit</button>
  </div>
</div>
</body>
</html>
