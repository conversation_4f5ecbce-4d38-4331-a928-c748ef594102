# Javascript API

MiniWoB++ environments incorporate a few JavaScript utilities,
many of which are used by the Python interface.

## `Math.seedrandom([seed])`

Sets the global random seed of the environment. The optional argument `seed` can be any object.

## `getDOMInfo()`

Returns a nested object containing information about the current DOM states.
The returned object corresponds to the `<body>` element. Its children can be accessed under the `children` field.

Each visible DOM element is converted into an object with the following fields:

* `tag` (string): Tag name
  * For normal elements, this is the uppercased tag name (e.g., `"DIV"`)
  * For `<input>` elements, the input type is appended (e.g., `"INPUT_text"`)
  * Each non-empty text node is converted into pseudo-elements with tag `"t"`,
    where each pseudo-element represents one line of text.
    However, if the text node is the only child of the parent. The text pseudo-element is not created,
    and its text is assigned to the parent element instead.
* `ref` (number): Reference number
  * Within each episode, the `ref` number of the same object stays the same
  * For the same random seed, the `ref` number of the same object should be the same
  * `ref` for normal elements start from 1, while `ref` for text psuedo-elements counts down from -1
* `children` (list): Recursive list of objects corresponding to the children
* `left`, `top`, `width`, `height` (number): Geometry of the element
* `id` (string): Element's `id`
* `classes` (string): Element's `class`es (space-separated)
* `bgColor`, `fgColor` (string): Background and foreground colors
* `focused` (boolean): Indicates if the element is being focused on
* `tampered` (boolean): Indicates if the element is tampered (clicked, focused, typed, etc.)
* `value`: For `<input>`, this contains the input value
  * For `checkbox` and `radio` types, this contains a boolean whether the input is selected
  * For other input types, this contains a text value
* `text` (string): For child nodes and text pseudo-elements, this contains the text content

## `flattenDOMInfo(rootDomInfo)`

Can be called on the result of `getDOMInfo()` to get a flattened representation.
Useful for debugging in Chrome console.

## `elementClick(ref)`

Clicks on an element regardless of its location and visibility.
The argument `ref` is the ref value generated by the previous call to `getDOMInfo()`.

## `visualizeAttention(values)`

Visualizes the attention weights on the screen.
The argument `values` is a 2D array of shape 20 × 20.
