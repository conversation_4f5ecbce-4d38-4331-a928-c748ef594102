---
name: Bug Report
about: Submit a bug report
title: "[Bug Report] Bug title"

---

If you are submitting a bug report, please fill in the following details and use the tag [bug].

**Describe the bug**
A clear and concise description of what the bug is.

**Code example**
Please try to provide a minimal example to reproduce the bug. Error messages and stack traces are also helpful.

**System Info**
Describe the characteristic of your environment:
 * Describe how miniwob-plusplus was installed (pip, docker, source, ...)
 * What OS/version of Linux you're using. Note that while we will accept PRs to improve Window's support, we do not officially support it.
 * Python version

**Additional context**
Add any other context about the problem here.

### Checklist

- [ ] I have checked that there is no similar [issue](https://github.com/Farama-Foundation/miniwob-plusplus/issues) in the repo (**required**)
