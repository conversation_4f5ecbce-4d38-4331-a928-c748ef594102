# This workflow will build and (if release) publish Python distributions to PyPI
# For more information see:
#   - https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions
#   - https://packaging.python.org/en/latest/guides/publishing-package-distribution-releases-using-github-actions-ci-cd-workflows/
#
# derived from https://github.com/Farama-Foundation/PettingZoo/blob/e230f4d80a5df3baf9bd905149f6d4e8ce22be31/.github/workflows/build-publish.yml
name: build-publish

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  release:
    types: [published]

jobs:
  build-wheels:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
        - os: ubuntu-latest
          python: 37
          platform: manylinux_x86_64
        - os: ubuntu-latest
          python: 38
          platform: manylinux_x86_64
        - os: ubuntu-latest
          python: 39
          platform: manylinux_x86_64
        - os: ubuntu-latest
          python: 310
          platform: manylinux_x86_64
        - os: ubuntu-latest
          python: 311
          platform: manylinux_x86_64

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
    - name: Install dependencies
      run: python -m pip install --upgrade pip setuptools build
    - name: Build sdist and wheels
      run: python -m build
    - name: Store wheels
      uses: actions/upload-artifact@v3
      with:
        path: dist

  publish:
    runs-on: ubuntu-latest
    needs:
    - build-wheels
    if: github.event_name == 'release' && github.event.action == 'published'
    steps:
    - name: Download dists
      uses: actions/download-artifact@v3
      with:
        name: artifact
        path: dist
    - name: Publish
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}
