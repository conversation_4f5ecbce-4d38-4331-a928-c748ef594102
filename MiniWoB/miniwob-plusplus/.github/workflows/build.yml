name: build
on: [pull_request, push]

permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11']
    steps:
      - uses: actions/checkout@v2
      - run: |
           docker build -f py.Dockerfile \
             --build-arg PYTHON_VERSION=${{ matrix.python-version }} \
             --tag miniwob-plusplus-docker .
      - name: Run tests
        run: docker run --shm-size=256m miniwob-plusplus-docker pytest --timeout=20
