import gymnasium
import miniwob
from miniwob.action import ActionTypes

gymnasium.register_envs(miniwob)
env = gymnasium.make('miniwob/click-test-2-v1', render_mode='human')

def policy(observation):
    # 找到文本为 "ONE" 的按钮并点击
    for element in observation["dom_elements"]:
        if element["text"] == "ONE":
            return env.unwrapped.create_action(ActionTypes.CLICK_ELEMENT, ref=element["ref"])
    return None  # 如果找不到按钮

try:
  observation, info = env.reset(seed=42)
  for _ in range(1000):
    action = policy(observation)  # User-defined policy function
    observation, reward, terminated, truncated, info = env.step(action)
    if terminated:
      observation, info = env.reset()
finally:
  env.close()