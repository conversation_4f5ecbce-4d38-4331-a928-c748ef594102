OPENAI_API_KEY=""
OPENAI_BASE_URL="https://api.openai.com/v1"

OPENAI_API_MODEL="gpt-4o"

HF_ENDPOINT = "https://hf-mirror.com"
#HTTP_PROXY="http://127.0.0.1:1080"

# 如果版本升级，是否清空知识库重新生成
EMPTY_KNOWLEDGE_BASE_WHEN_VERSION_UPGRADE = "False"
# 如果版本降级，是否清空知识库重新生成
EMPTY_KNOWLEDGE_BASE_WHEN_VERSION_DOWNGRADE = "True"

KNOWLEDGE_BASE_ABSOLUTE_ROOT_PATH = "./knowledge_base"

SERVER_EMBEDDING_DEVICE="cuda" # "cuda" 或者 "cpu" 或者 "mps"
SERVER_EMBEDDING_ENDPOINT="http://127.0.0.1:8765" # 用于 embedding 服务的地址，详见 utils\embedding_pipeline.py
CLIENT_EMBEDDING_DEVICE="server" # "cuda" 或者 "cpu" 或者 "mps" 或者 "server" ， 如果填写 "server" 则需要运行 python -m utils.embedding_pipeline

TURN_ON_DEMO_MODE="True" # 是否开启 demo 模式
MESSAGE_SERVER_ENDPOINT="http://127.0.0.1:8768" # see demo/demo_web_backend.py
DEMO_BACKEND_ENDPOINT="http://127.0.0.1:8767" # see demo/demo_agent_backend.py
RAG_SERVER_ENDPOINT="http://127.0.0.1:8769" # see utils/retrieval.py

LOW_RESOLUTION="False" # 是否开启低分辨率模式，开启后会将屏幕截图降低分辨率，减少Token数量
