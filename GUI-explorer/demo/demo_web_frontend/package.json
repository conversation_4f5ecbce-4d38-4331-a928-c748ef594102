{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"ant-design-vue": "4.x", "pinia": "~3.0.1", "vue": "3.5.13", "vue-router": "~4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "vite": "^6.2.5", "vite-plugin-vue-devtools": "^7.7.2"}}