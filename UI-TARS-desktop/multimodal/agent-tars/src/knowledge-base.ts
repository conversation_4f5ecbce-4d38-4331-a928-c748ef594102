// ===== Special Knowledge Base for Loop Prevention =====
const SPECIAL_KNOWLEDGE_BASE = [
    //   {
    //     id: 'spothero_calendar_left_arrow_click_loop',
    //     category: 'click_loop',
    //     condition:
    //       'Repeatedly clicking the left arrow of the calendar without reaching the target month',
    //     solution: `
    // 🚨 **EMERGENCY OVERRIDE - CLICK LOOP RESOLUTION** 🚨
    
    // **Core Idea**: Before each click, read the currently displayed month/year, identify the NEXT AVALIABLE month/year(e.g. If current is July 2025, and you need June, you need June 2026 (next available June), NOT June 2025 (past June)),NOT THE PAST month/year(e.g. June 2025). Compute how many months you are away from your target, then pick the shortest direction.
    
    // **Corrective Steps**:
    // 1. 🔍 **Read the calendar header** to see current month/year (e.g., "I see July 2025")
    // 2. Identify the target month/year (e.g., If current is July 2025, and you need June, you need June 2026 (next available June), NOT June 2025 (past June))
    // 3. 🧮 **Compute the month difference** to your goal (June 2025 or June 2026):
    //    \`\`\`
    //    delta = (targetYear - currentYear) * 12
    //          + (targetMonthIndex - currentMonthIndex)
    //    \`\`\`
    // 4. ↔️ **Choose direction**:
    //    - If \`delta > 0\`, click the **right arrow** (move forward in time).  
    //    - If \`delta < 0\`, click the **left arrow** (move backward).  
    // 5. 🔄 **Repeat steps 1-3** until \`delta == 0\` (you're on the target month).  
    // 6. ✅ **Select the day** (e.g. click “18”).
    
    // **Why This Works**: Booking systems only show future dates, so you can't choose past dates.
    // `,
    //   },
      {
        id: 'healthline_click_scroll_loop',
        category: 'click_loop',
        condition:
          'Repeatedly clicking buttons (next, submit, load more, etc.) on healthline or similar content sites without progress',
        solution: `
      🚨 **EMERGENCY OVERRIDE - CLICK SCROLL LOOP RESOLUTION** 🚨
    
      **Core Idea**: When repeatedly clicking buttons without progress on content sites like healthline, the target element might be out of viewport, obscured, or requires scrolling to activate. Always scroll down to reveal hidden content before attempting clicks.
    
      **Corrective Steps**:
      1. 🔍 **Check current viewport**: Verify if the clicked element is visible and clickable in the current view.
      2. ⬇️ **Scroll down first**: Use \`browser_scroll\` tool to scroll down the page to reveal more content and ensure the target element is fully visible.
    
      **Why This Works**: 
      - Scrolling ensures elements are in the viewport and triggers any scroll-based events.
      - Many buttons become active only after content is fully loaded through scrolling.
      `,
      },
      {
        id: 'general_click_loop',
        category: 'click_loop',
        condition: 'Repeatedly clicking any button or element without progress on general websites',
        solution: `
      🚨 **EMERGENCY OVERRIDE - GENERAL CLICK LOOP RESOLUTION** 🚨
    
      **Core Idea**: When repeatedly clicking any element without progress, systematically check viewport visibility, scroll to ensure accessibility, and verify element state before re-attempting the action.
      `,
      },
      {
        id: 'healthline_wait_loop',
        category: 'wait_loop',
        condition:
          'Repeatedly waiting for page load without scrolling to check if the button is out of the viewport',
        solution: `
    🚨 **EMERGENCY OVERRIDE - WAIT LOOP RESOLUTION** 🚨
    
    **Core Idea**: When the page repeatedly doing something without progress (e.g. repeated wait() actions), automatically scroll the page to ensure all elements are visible, and continue operations when content updates are detected.
    
    **Corrective Steps**:
    1. 🔍 **Wait for page load**: After an input or page update, first check if the page is fully loaded, ensuring all necessary elements are displayed.
    2. ⬇️ **Auto scroll**: use \`browser_scroll\` tool to scroll the page.
    3. ⏳ **Wait for page load**:
        - If no new content has been loaded, continue waiting and repeat step 2.
        - If new content has been loaded, proceed with subsequent actions.
    4. 🔄 **Continue with operations**: Once the page has fully loaded, proceed with the next steps (such as input, click, etc.).
    
    **Why This Works**:
      - Auto-scrolling ensures all page elements are loaded into the viewport, avoiding operation failures due to hidden elements.
      - By constantly checking for page changes, you ensure actions are performed when the page is in the correct state, avoiding redundant waiting or incorrect inputs.
    `,
      },
      {
        id: 'general_wait_loop',
        category: 'wait_loop',
        condition:
          'Repeatedly waiting for page changes or content loading without progress on general websites',
        solution: `
    🚨 **EMERGENCY OVERRIDE - GENERAL WAIT LOOP RESOLUTION** 🚨
    
    **Core Idea**: When repeatedly waiting for page changes or content loading without progress, systematically check viewport visibility, scroll to ensure accessibility, and verify element state before re-attempting the action.
    `,
      },
    ];
    
    function detectLoopInMessageHistory(messageHistory: any[]): boolean {
      // 检查最新的assistant消息是否包含LOOP_DETECTED（LLM自检上报）
      const lastMessage = messageHistory[messageHistory.length - 1];
      if (lastMessage?.role === 'assistant' && typeof lastMessage.content === 'string') {
        const content = lastMessage.content.toLowerCase();
        return content.includes('loop_detected');
      }
      return false;
    }
    
    function extractLoopTypeFromHistory(messageHistory: any[]): string {
      const lastMessage = messageHistory[messageHistory.length - 1];
      if (lastMessage?.role === 'assistant' && typeof lastMessage.content === 'string') {
        const content = lastMessage.content.toLowerCase();
        if (content.includes('click')) return 'click';
        if (content.includes('wait')) return 'wait';
      }
      return '';
    }
    
    function generateLoopKnowledge(messageHistory?: any[]): string {
      // 1. 没有历史，直接返回通用
      if (!messageHistory) {
        // 优先通用点击，其次日历
        return (
          SPECIAL_KNOWLEDGE_BASE.find((k) => k.id === 'general_click_loop')?.solution || 
          'Fallback solution here'
        );
      }
    
      // 2. 提取 loop 类型
      const loopType = extractLoopTypeFromHistory(messageHistory);
    
      // 3. 如果是 click 相关，拼接所有 click 相关 solution
      if (loopType.includes('click')) {
        return SPECIAL_KNOWLEDGE_BASE.filter((k) => k.category === 'click_loop')
          .map((k) => k.solution)
          .join('\n\n');
      }
      // 4. 如果是 wait 相关，拼接所有 wait 相关 solution
      if (loopType.includes('wait')) {
        return SPECIAL_KNOWLEDGE_BASE.filter((k) => k.category === 'wait_loop')
          .map((k) => k.solution)
          .join('\n\n');
      }
    
      return '';
    }
    
    export function buildSystemPrompt(messageHistory?: any[]): string {
      const loopDetected = messageHistory ? detectLoopInMessageHistory(messageHistory) : false;
      const knowledgeContent = loopDetected ? generateLoopKnowledge(messageHistory) : '';
    
      const basePrompt = DEFAULT_SYSTEM_PROMPT;
      if (knowledgeContent) {
        return `🚨🚨🚨 **EMERGENCY OVERRIDE - STOP AND REFLECT** 🚨🚨🚨\n\n${knowledgeContent}\n\n**🚨 CRITICAL**: Follow the above specialized knowledge immediately. Pause, reflect, and take the correct action NOW.\n\n================================================================================\n\n${basePrompt}`;
      }
      return basePrompt;
    }
    