#!/usr/bin/env node

/**
 * Test script to verify emergency override functionality
 * This script simulates the loop detection scenario without requiring external APIs
 */

const { buildSystemPrompt, detectLoopInMessageHistory, generateLoopKnowledge } = require('./dist/prompt.js');

// Mock message history with loop detection
const mockHistoryWithLoop = [
  {
    role: 'user',
    content: 'Find parking near the San Francisco Museum of Modern Art'
  },
  {
    role: 'assistant',
    content: 'I will help you find parking. Let me navigate to the website.'
  },
  {
    role: 'assistant',
    content: 'LOOP_DETECTED: 重复点击日历箭头切换月份操作无有效进展（在七月和八月间来回切换，未到目标六月）\nROOT_CAUSE_ANALYSIS: 点击方向混乱（误点左右箭头）且之前左箭头点击未生效可能因交互问题，导致月份偏离目标\nLOGICAL_ERROR_DETECTED: 操作逻辑错误，切换月份方向混乱且重复无效点击未推进选择六月18日的任务'
  }
];

// Mock history without loop
const mockHistoryWithoutLoop = [
  {
    role: 'user',
    content: 'Find parking near the San Francisco Museum of Modern Art'
  },
  {
    role: 'assistant',
    content: 'I will help you find parking. Let me navigate to the website.'
  }
];

console.log('🧪 Testing Emergency Override Functionality\n');

// Test 1: Loop detection
console.log('Test 1: Loop Detection');
const loopDetected = detectLoopInMessageHistory(mockHistoryWithLoop);
const noLoopDetected = detectLoopInMessageHistory(mockHistoryWithoutLoop);

console.log(`✅ Loop detected in history with loop: ${loopDetected}`);
console.log(`✅ No loop detected in normal history: ${!noLoopDetected}`);

// Test 2: Knowledge generation
console.log('\nTest 2: Knowledge Generation');
const loopKnowledge = generateLoopKnowledge(mockHistoryWithLoop);
console.log(`✅ Loop knowledge generated: ${loopKnowledge.length > 0}`);
console.log(`📝 Knowledge content preview: ${loopKnowledge.substring(0, 100)}...`);

// Test 3: System prompt building
console.log('\nTest 3: System Prompt Building');
const promptWithLoop = buildSystemPrompt(mockHistoryWithLoop);
const promptWithoutLoop = buildSystemPrompt(mockHistoryWithoutLoop);

const hasEmergencyOverride = promptWithLoop.includes('🚨🚨🚨 **EMERGENCY OVERRIDE');
const noEmergencyOverride = !promptWithoutLoop.includes('🚨🚨🚨 **EMERGENCY OVERRIDE');

console.log(`✅ Emergency override in loop prompt: ${hasEmergencyOverride}`);
console.log(`✅ No emergency override in normal prompt: ${noEmergencyOverride}`);

// Test 4: Show the emergency override content
if (hasEmergencyOverride) {
  console.log('\n🚨 Emergency Override Content:');
  const overrideMatch = promptWithLoop.match(/🚨🚨🚨[\s\S]+?(?=\n=+)/);
  if (overrideMatch) {
    console.log(overrideMatch[0]);
  }
}

// Summary
console.log('\n📊 Test Summary:');
const allTestsPassed = loopDetected && !noLoopDetected && loopKnowledge.length > 0 && hasEmergencyOverride && noEmergencyOverride;
console.log(`Overall result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

if (allTestsPassed) {
  console.log('\n🎉 Emergency override functionality is working correctly!');
  console.log('The system will now inject specialized knowledge when loops are detected.');
} else {
  console.log('\n❌ There are issues with the emergency override functionality.');
  process.exit(1);
}
